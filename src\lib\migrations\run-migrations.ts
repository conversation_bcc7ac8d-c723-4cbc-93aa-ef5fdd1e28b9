import { migrateProjects } from './migrate-projects';
import { migrateServices } from './migrate-services';

export async function runAllMigrations() {
  console.log('🚀 Starting data migration...');
  
  try {
    // Run project migration
    console.log('\n📁 Migrating projects...');
    const projectResult = await migrateProjects();
    if (!projectResult.success) {
      throw new Error(`Project migration failed: ${projectResult.error}`);
    }
    
    // Run services migration
    console.log('\n🔧 Migrating services...');
    const serviceResult = await migrateServices();
    if (!serviceResult.success) {
      throw new Error(`Service migration failed: ${serviceResult.error}`);
    }
    
    console.log('\n✅ All migrations completed successfully!');
    return { success: true, message: 'All data migrated successfully' };
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    return { success: false, error };
  }
}

// Function to run migrations from admin panel
export async function runMigrationsFromAdmin() {
  const result = await runAllMigrations();
  
  if (result.success) {
    return {
      success: true,
      message: 'Data migration completed successfully. All existing projects and services have been transferred to the database.',
      details: {
        projects: 'All project data including images, features, and metadata migrated',
        services: 'All service offerings with categories, features, and benefits migrated'
      }
    };
  } else {
    return {
      success: false,
      message: 'Migration failed. Please check the console for details.',
      error: result.error
    };
  }
}
