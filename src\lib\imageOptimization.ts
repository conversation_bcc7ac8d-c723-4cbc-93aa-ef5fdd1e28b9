/**
 * Image optimization utilities to improve website performance
 */

// Type for responsive image sizes
interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  widths?: number[];
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
}

// Default widths for responsive images
const DEFAULT_WIDTHS = [320, 640, 768, 1024, 1280];

/**
 * Creates a responsive image srcset string from a base image URL and widths
 * @param src Base image URL
 * @param widths Array of image widths to include in srcset
 * @returns Formatted srcset string
 */
export function createSrcSet(src: string, widths: number[] = DEFAULT_WIDTHS): string {
  if (!src) return '';
  
  // Don't process external URLs or SVGs
  if (src.startsWith('http') || src.endsWith('.svg')) {
    return src;
  }
  
  // Extract file extension
  const extension = src.split('.').pop() || 'jpg';
  const basePath = src.substring(0, src.lastIndexOf('.'));
  
  // Generate srcset entries for each width
  return widths
    .map(width => `${basePath}-${width}w.${extension} ${width}w`)
    .join(', ');
}

/**
 * Creates an optimized image element with proper attributes for performance
 * @param src Image source URL
 * @param alt Alt text for image
 * @param className CSS class(es) for image
 * @returns HTML string for the image element with all optimization attributes
 */
export function createOptimizedImageHtml(
  props: ResponsiveImageProps
): string {
  const { 
    src, 
    alt, 
    className = '', 
    sizes = '100vw', 
    widths = DEFAULT_WIDTHS,
    loading = 'lazy',
    decoding = 'async'
  } = props;
  
  const srcset = createSrcSet(src, widths);
  
  return `
    <img 
      src="${src}" 
      ${srcset ? `srcset="${srcset}"` : ''} 
      sizes="${sizes}" 
      alt="${alt}" 
      class="${className}" 
      loading="${loading}" 
      decoding="${decoding}"
    />
  `;
}

/**
 * Replaces a standard image URL with an optimized image path for specific width
 * @param src Original image source
 * @param width Target width
 * @returns Optimized image path
 */
export function getOptimizedImagePath(src: string, width: number): string {
  if (!src || src.startsWith('http') || src.endsWith('.svg')) {
    return src;
  }
  
  const extension = src.split('.').pop() || 'jpg';
  const basePath = src.substring(0, src.lastIndexOf('.'));
  
  return `${basePath}-${width}w.${extension}`;
}

/**
 * Detects and replaces all images with data-src attributes with optimized ones
 * This is an alternative to IntersectionObserver for legacy browsers
 */
export function optimizeImagesOnLoad(): void {
  if (typeof document === 'undefined') return;
  
  const processImages = (): void => {
    // Find all images with data-src or regular src
    const images = document.querySelectorAll('img[data-src], img:not([data-src])');
    
    images.forEach((img: HTMLImageElement) => {
      // Skip already processed images
      if (img.dataset.processed === 'true') return;
      
      // Process data-src images first
      if (img.dataset.src) {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
      }
      
      // Add missing attributes for performance
      if (!img.getAttribute('loading')) {
        img.setAttribute('loading', 'lazy');
      }
      
      if (!img.getAttribute('decoding')) {
        img.setAttribute('decoding', 'async');
      }
      
      // Mark as processed
      img.dataset.processed = 'true';
    });
  };
  
  // Process immediately for visible images
  processImages();
  
  // Process again when DOM is fully loaded
  if (document.readyState === 'complete') {
    processImages();
  } else {
    document.addEventListener('DOMContentLoaded', processImages);
    window.addEventListener('load', processImages);
  }
}

/**
 * Sets up lazy loading for background images with data-bg-src attributes
 */
export function setupBackgroundLazyLoading(): void {
  if (typeof document === 'undefined' || !('IntersectionObserver' in window)) {
    return;
  }
  
  const bgElements = document.querySelectorAll('[data-bg-src]');
  
  if (bgElements.length === 0) return;
  
  const bgObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        const bgSrc = element.dataset.bgSrc;
        
        if (bgSrc) {
          element.style.backgroundImage = `url('${bgSrc}')`;
          element.removeAttribute('data-bg-src');
        }
        
        bgObserver.unobserve(element);
      }
    });
  });
  
  bgElements.forEach(element => {
    bgObserver.observe(element);
  });
} 