<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Not Found - Rodelas Construction Services</title>
  <link rel="icon" type="image/jpg" href="/rcslogo.jpg" />
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
      text-align: center;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 20px;
    }
    h1 {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: #0553a1; /* RCS Blue */
    }
    p {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 25px;
    }
    .button {
      display: inline-block;
      background-color: #0553a1; /* RCS Blue */
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 600;
      transition: background-color 0.3s;
    }
    .button:hover {
      background-color: #043b70;
    }
    .timer {
      margin-top: 20px;
      font-size: 0.9rem;
      color: #666;
    }
  </style>
  <script>
    // Redirect to homepage after brief delay
    window.onload = function() {
      const urlPath = window.location.pathname;
      let count = 2;
      const timer = document.getElementById('timer');
      
      const interval = setInterval(() => {
        count--;
        if (timer) timer.textContent = count;
        
        if (count <= 0) {
          clearInterval(interval);
          // Remove any query parameters or hash
          const baseUrl = window.location.origin;
          // Preserve the current path to allow React Router to handle it
          window.location.href = baseUrl + (urlPath || '/');
        }
      }, 1000);
    };
  </script>
</head>
<body>
  <div class="container">
    <img src="/rcslogo.jpg" alt="Rodelas Construction Services Logo" class="logo">
    <h1>Page Not Found</h1>
    <p>It seems like you're trying to access a page directly. We'll redirect you to the proper page in a moment.</p>
    <a href="/" class="button">Go to Homepage</a>
    <div class="timer">Redirecting in <span id="timer">2</span> seconds...</div>
  </div>
</body>
</html> 