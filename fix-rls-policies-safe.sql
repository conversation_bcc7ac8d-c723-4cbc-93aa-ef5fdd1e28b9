-- =====================================================
-- SAFE FIX: RLS Policies Setup (No Storage Policies)
-- Run this in Supabase SQL Editor to fix database errors
-- =====================================================

-- 1. DISABLE RLS temporarily to allow admin operations
ALTER TABLE IF EXISTS projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS services DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS company_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS media_files DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS admin_users DISABLE ROW LEVEL SECURITY;

-- 2. DROP EXISTING POLICIES (if they exist)
DROP POLICY IF EXISTS "Allow all operations" ON projects;
DROP POLICY IF EXISTS "Allow all operations" ON services;
DROP POLICY IF EXISTS "Allow all operations" ON company_info;
DROP POLICY IF EXISTS "Allow all operations" ON blog_posts;
DROP POLICY IF EXISTS "Allow all operations" ON media_files;
DROP POLICY IF EXISTS "Allow all operations" ON admin_users;

-- 3. SIMPLE RLS POLICIES FOR TABLES (Allow all operations)
-- Projects
CREATE POLICY "Allow all operations" ON projects FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Services  
CREATE POLICY "Allow all operations" ON services FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE services ENABLE ROW LEVEL SECURITY;

-- Company Info
CREATE POLICY "Allow all operations" ON company_info FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE company_info ENABLE ROW LEVEL SECURITY;

-- Blog Posts
CREATE POLICY "Allow all operations" ON blog_posts FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Media Files
CREATE POLICY "Allow all operations" ON media_files FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Admin Users
CREATE POLICY "Allow all operations" ON admin_users FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 4. GRANT PERMISSIONS (only for tables we own)
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 5. CREATE ADMIN USER (if not exists)
INSERT INTO admin_users (email, password_hash, role, is_active)
VALUES (
  '<EMAIL>',
  '$2a$10$rZ8Q8Q8Q8Q8Q8Q8Q8Q8Q8O', -- placeholder hash
  'admin',
  true
)
ON CONFLICT (email) DO NOTHING;

-- 6. INSERT DEFAULT COMPANY INFO (if not exists)
INSERT INTO company_info (field_name, field_value)
VALUES 
  ('company_name', 'Rodelas Construction Services'),
  ('company_description', 'Professional construction services in the Philippines'),
  ('company_email', '<EMAIL>'),
  ('company_phone', '+63 ************'),
  ('company_address', 'Philippines')
ON CONFLICT (field_name) DO UPDATE SET field_value = EXCLUDED.field_value;

-- 7. VERIFY SETUP
SELECT 'Tables with RLS enabled:' as status;
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

SELECT 'RLS policies created:' as status;
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public';

-- 8. TEST QUERIES
SELECT 'Testing projects table:' as status;
SELECT COUNT(*) as project_count FROM projects;

SELECT 'Testing services table:' as status;
SELECT COUNT(*) as service_count FROM services;

SELECT 'Testing company_info table:' as status;
SELECT COUNT(*) as company_info_count FROM company_info;

-- 9. SUCCESS MESSAGE
SELECT 'Database setup completed successfully!' as final_status;
