-- =====================================================
-- FIX SERVICES TABLE - Add missing columns
-- Run this to fix the services table structure
-- =====================================================

-- Add missing columns to services table
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;

ALTER TABLE services 
ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 1;

-- Update the database types to match
SELECT 'Services table structure after fix:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'services' 
ORDER BY ordinal_position;

SELECT 'Services table fix completed!' as status;
