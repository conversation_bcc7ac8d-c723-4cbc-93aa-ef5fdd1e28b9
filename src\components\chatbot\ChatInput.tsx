import React, { RefObject, useEffect, useState } from 'react';
import { Send } from 'lucide-react';

interface ChatInputProps {
  message: string;
  setMessage: (message: string) => void;
  sendMessage: (e: React.FormEvent) => void;
  isLoading: boolean;
  inputRef: RefObject<HTMLInputElement>;
}

// Common emotions/moods with emoji suggestions
const emojiSuggestions = [
  { keywords: ['happy', 'joy', 'glad', 'good'], emoji: '😊' },
  { keywords: ['sad', 'upset', 'unhappy'], emoji: '😔' },
  { keywords: ['angry', 'mad', 'furious'], emoji: '😠' },
  { keywords: ['confused', 'unsure', 'not sure'], emoji: '😕' },
  { keywords: ['thanks', 'thank you', 'salamat'], emoji: '🙏' },
  { keywords: ['wow', 'amazing', 'awesome', 'great'], emoji: '🤩' },
  { keywords: ['house', 'home', 'bahay'], emoji: '🏠' },
  { keywords: ['building', 'office', 'commercial'], emoji: '🏢' },
  { keywords: ['money', 'budget', 'cost', 'price'], emoji: '💰' },
  { keywords: ['soon', 'quickly', 'fast'], emoji: '⏱️' },
];

const ChatInput: React.FC<ChatInputProps> = ({ 
  message, 
  setMessage, 
  sendMessage, 
  isLoading,
  inputRef
}) => {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [suggestedEmoji, setSuggestedEmoji] = useState<string | null>(null);
  const [isContactFormVisible, setIsContactFormVisible] = useState(false);
  
  // Check if a contact form is visible in the messages
  useEffect(() => {
    // We don't have direct access to messages here, but we can check DOM
    const contactForm = document.querySelector('.chat-container .chat-contact-form');
    setIsContactFormVisible(!!contactForm);
  }, []);
  
  // Detect if the keyboard is likely visible based on viewport changes
  useEffect(() => {
    const initialHeight = window.innerHeight;
    
    const handleResize = () => {
      const currentHeight = window.innerHeight;
      // If viewport is significantly smaller, keyboard is likely visible
      setIsKeyboardVisible(currentHeight < initialHeight * 0.8);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Check for emoji suggestions based on input
  useEffect(() => {
    if (message.trim()) {
      const words = message.toLowerCase().split(' ');
      for (const word of words) {
        const suggestion = emojiSuggestions.find(s => 
          s.keywords.some(keyword => keyword.toLowerCase() === word.toLowerCase())
        );
        if (suggestion) {
          setSuggestedEmoji(suggestion.emoji);
          return;
        }
      }
    }
    setSuggestedEmoji(null);
  }, [message]);

  // Handle emoji insertion
  const insertEmoji = () => {
    if (suggestedEmoji) {
      setMessage(`${message} ${suggestedEmoji} `);
      setSuggestedEmoji(null);
      inputRef.current?.focus();
    }
  };

  // Handle form submission with Enter key and prevent default behavior
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (message.trim()) {
        sendMessage(e as any);
      }
    }
  };

  return (
    <form 
      onSubmit={(e) => {
        e.preventDefault();
        if (message.trim()) {
          sendMessage(e);
        }
      }} 
      className={`border-t border-gray-200 p-3 bg-white shadow-sm sticky bottom-0 left-0 right-0 z-10 ${
        isKeyboardVisible ? 'pb-3' : 'pb-safe' // Use safe-area-inset if keyboard not visible
      } ${isContactFormVisible ? 'chat-input-with-form' : ''}`}
      style={{ 
        paddingBottom: `max(0.75rem, env(safe-area-inset-bottom))` 
      }}
    >
      {isContactFormVisible && (
        <div className="text-xs text-center text-blue-600 mb-2">
          You can still ask a question here
        </div>
      )}
      
      {suggestedEmoji && (
        <div className="flex justify-start mb-2">
          <button
            type="button"
            onClick={insertEmoji}
            className="px-2 py-1 rounded-full bg-blue-50 text-blue-700 text-sm flex items-center gap-1 hover:bg-blue-100"
            aria-label="Insert suggested emoji"
          >
            {suggestedEmoji} Add emoji
          </button>
        </div>
      )}

      <div className="flex gap-2 items-center">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={isContactFormVisible ? "Ask a different question..." : "Type your message..."}
          className={`flex-grow border border-gray-200 rounded-full px-3 py-2.5 md:px-4 md:py-3 focus:outline-none focus:ring-2 focus:ring-rcs-blue text-base bg-gray-50 transition-all duration-300 hover:shadow-sm focus:shadow-md ${
            isContactFormVisible ? 'border-blue-300 hover:border-blue-400' : ''
          }`}
          disabled={isLoading}
          ref={inputRef}
          autoComplete="off"
          autoCorrect="on"
          spellCheck="true"
          aria-label="Chat message"
        />
        
        <button
          type="submit"
          className="bg-gradient-to-r from-rcs-blue to-blue-700 text-white p-2 md:p-3 rounded-full hover:shadow-lg transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed min-w-[42px] md:min-w-[48px] h-[42px] md:h-[48px] flex items-center justify-center hover:scale-105 flex-shrink-0 shadow-md"
          disabled={isLoading || !message.trim()}
          aria-label="Send message"
        >
          <Send size={18} className="transition-transform duration-300" />
        </button>
      </div>
    </form>
  );
};

export default ChatInput; 