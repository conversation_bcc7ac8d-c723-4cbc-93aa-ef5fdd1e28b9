import React from 'react';
import { Message } from './types';
import { MessageSquare } from 'lucide-react';

interface MinimizedPreviewProps {
  messages: Message[];
}

const MinimizedPreview: React.FC<MinimizedPreviewProps> = ({ messages }) => {
  if (messages.length === 0) return null;

  const lastMessage = messages[messages.length - 1];
  
  return (
    <div className="px-4 py-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 text-gray-600 text-sm overflow-hidden shadow-inner relative">
      <div className="flex items-center">
        <div className="w-6 h-6 bg-gradient-to-r from-rcs-blue to-blue-700 rounded-full flex items-center justify-center mr-3 shadow-sm">
          <MessageSquare className="h-3 w-3 text-white" />
        </div>
        
        <div className="overflow-hidden text-ellipsis max-w-full font-medium">
          {lastMessage.content.length > 40 
            ? `${lastMessage.content.substring(0, 40)}...` 
            : lastMessage.content
          }
        </div>
        
        <div className="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse shadow-sm"></div>
      </div>
    </div>
  );
};

export default MinimizedPreview; 