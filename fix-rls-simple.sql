-- =====================================================
-- SUPER SIMPLE FIX: Just fix the RLS policies
-- Run this in Supabase SQL Editor
-- =====================================================

-- 1. DISABLE RLS on all tables
ALTER TABLE IF EXISTS projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS services DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS company_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS media_files DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS admin_users DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL EXISTING POLICIES
DROP POLICY IF EXISTS "Allow all operations" ON projects;
DROP POLICY IF EXISTS "Allow all operations" ON services;
DROP POLICY IF EXISTS "Allow all operations" ON company_info;
DROP POLICY IF EXISTS "Allow all operations" ON blog_posts;
DROP POLICY IF EXISTS "Allow all operations" ON media_files;
DROP POLICY IF EXISTS "Allow all operations" ON admin_users;

-- 3. CREATE SIMPLE POLICIES (Allow everything)
CREATE POLICY "Allow all" ON projects FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all" ON services FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all" ON company_info FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all" ON blog_posts FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all" ON media_files FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all" ON admin_users FOR ALL USING (true) WITH CHECK (true);

-- 4. ENABLE RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 5. GRANT PERMISSIONS
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- 6. TEST QUERIES
SELECT 'Testing projects:' as test;
SELECT COUNT(*) FROM projects;

SELECT 'Testing services:' as test;
SELECT COUNT(*) FROM services;

SELECT 'RLS Setup Complete!' as status;
