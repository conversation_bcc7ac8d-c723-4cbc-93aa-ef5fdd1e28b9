import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { auth } from '@/lib/supabase';
import { db } from '@/lib/database';
import type { AdminUser } from '@/lib/database.types';

interface AuthContextType {
  user: User | null;
  adminUser: AdminUser | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    const initializeAuth = async () => {
      try {
        const { session: initialSession } = await auth.getCurrentSession();
        
        if (initialSession) {
          setSession(initialSession);
          setUser(initialSession.user);
          
          // Fetch admin user data
          try {
            const adminData = await db.adminUsers.getById(initialSession.user.id);
            setAdminUser(adminData);
          } catch (error) {
            console.error('Error fetching admin user data:', error);
            // If we can't fetch admin data, still allow the session but without admin privileges
            setAdminUser(null);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        try {
          // Fetch admin user data when user signs in
          const adminData = await db.adminUsers.getById(session.user.id);
          setAdminUser(adminData);
        } catch (error) {
          console.error('Error fetching admin user data:', error);
          setAdminUser(null);
        }
      } else {
        setAdminUser(null);
      }
      
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await auth.signIn(email, password);
      
      if (error) {
        return { error };
      }

      // Update last login time in database
      if (data.user) {
        try {
          // Update admin user's last login
          await db.adminUsers.updateLastLogin(data.user.id);
          console.log('User signed in successfully:', data.user.email);
        } catch (dbError) {
          console.error('Error updating last login:', dbError);
          // Don't fail the login if we can't update the timestamp
        }
      }

      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    setLoading(true);
    try {
      await auth.signOut();
      setUser(null);
      setAdminUser(null);
      setSession(null);
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  // Computed properties
  const isAuthenticated = !!user && !!session;
  const isAdmin = !!adminUser && adminUser.role === 'admin';

  const value: AuthContextType = {
    user,
    adminUser,
    session,
    loading,
    signIn,
    signOut,
    isAuthenticated,
    isAdmin,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook for checking if user is authenticated
export const useRequireAuth = () => {
  const { isAuthenticated, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      // Redirect to login page
      window.location.href = '/admin/login';
    }
  }, [isAuthenticated, loading]);

  return { isAuthenticated, loading };
};

// Hook for checking if user is admin
export const useRequireAdmin = () => {
  const { isAdmin, loading, isAuthenticated } = useAuth();
  
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        window.location.href = '/admin/login';
      } else if (!isAdmin) {
        // Redirect to unauthorized page or show error
        console.error('Access denied: Admin privileges required');
        window.location.href = '/';
      }
    }
  }, [isAdmin, loading, isAuthenticated]);

  return { isAdmin, loading };
};
