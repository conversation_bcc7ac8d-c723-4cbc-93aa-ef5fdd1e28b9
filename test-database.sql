-- =====================================================
-- TEST DATABASE OPERATIONS
-- Run this to test if everything is working
-- =====================================================

-- 1. Test inserting a project directly
INSERT INTO projects (title, description, category, location, completion_date, status) 
VALUES ('Test Project Direct', 'Testing direct database insert', 'Residential', 'Manila', '2024-01-15', 'completed');

-- 2. Check if the project was inserted
SELECT 'Projects in database:' as status;
SELECT id, title, category, location, created_at FROM projects ORDER BY created_at DESC LIMIT 5;

-- 3. Test inserting a service
INSERT INTO services (title, description, category) 
VALUES ('Test Service Direct', 'Testing direct service insert', 'construction');

-- 4. Check if the service was inserted
SELECT 'Services in database:' as status;
SELECT id, title, category, created_at FROM services ORDER BY created_at DESC LIMIT 5;

-- 5. Check table permissions
SELECT 'Table permissions:' as status;
SELECT grantee, privilege_type, table_name 
FROM information_schema.role_table_grants 
WHERE table_name IN ('projects', 'services') 
AND grantee IN ('anon', 'authenticated', 'service_role');

-- 6. Check RLS policies
SELECT 'RLS policies:' as status;
SELECT schemaname, tablename, policyname, cmd, permissive, roles, qual 
FROM pg_policies 
WHERE tablename IN ('projects', 'services');

-- 7. Success message
SELECT 'Database test completed!' as final_status;
