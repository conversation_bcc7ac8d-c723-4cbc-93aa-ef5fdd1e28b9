import React from 'react';
import { MessageSquare } from 'lucide-react';
import { TextShimmerWave } from '@/components/ui/text-shimmer-wave';

const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-start">
      <div className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-gradient-to-r from-rcs-blue to-blue-700 flex items-center justify-center mr-2 shadow-md">
        <MessageSquare className="h-3.5 w-3.5 md:h-4 md:w-4 text-white" />
      </div>
      <div className="flex items-end mb-1.5">
        <div className="bg-white rounded-2xl px-4 py-2.5 border border-gray-100 shadow-sm max-w-xs md:max-w-sm">
          <TextShimmerWave 
            className="text-blue-600 font-medium text-sm"
            duration={1.2}
            spread={1.5}
          >
            Thinking
          </TextShimmerWave>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator; 