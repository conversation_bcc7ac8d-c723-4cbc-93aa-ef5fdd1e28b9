import { useState, useEffect, useRef, RefObject } from 'react';

interface UseLazyLoadOptions {
  rootMargin?: string;
  threshold?: number;
  triggerOnce?: boolean;
  disabled?: boolean;
}

interface UseLazyLoadReturn {
  isInView: boolean;
  ref: RefObject<HTMLElement>;
}

/**
 * Custom hook for lazy loading elements when they enter the viewport
 * 
 * @param options Configuration options for the IntersectionObserver
 * @returns Object containing ref to attach to the element and boolean indicating if element is in view
 * 
 * Example usage:
 * ```tsx
 * const { ref, isInView } = useLazyLoad({ triggerOnce: true, rootMargin: '100px' });
 * 
 * return (
 *   <div ref={ref}>
 *     {isInView ? <HeavyComponent /> : <PlaceholderComponent />}
 *   </div>
 * );
 * ```
 */
export function useLazyLoad({
  rootMargin = '0px',
  threshold = 0.1,
  triggerOnce = true,
  disabled = false,
}: UseLazyLoadOptions = {}): UseLazyLoadReturn {
  const [isInView, setIsInView] = useState(false);
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  
  useEffect(() => {
    // Skip if disabled or element already in view and triggerOnce is true
    if (disabled || (isInView && triggerOnce)) {
      return;
    }
    
    // Cleanup previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }
    
    // Setup new observer
    const element = elementRef.current;
    if (!element || typeof IntersectionObserver === 'undefined') {
      // Fallback if IntersectionObserver is not supported
      setIsInView(true);
      return;
    }
    
    // Create and configure new observer
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          
          // Disconnect observer if triggerOnce is true
          if (triggerOnce) {
            observer.disconnect();
            observerRef.current = null;
          }
        } else if (!triggerOnce) {
          setIsInView(false);
        }
      },
      { rootMargin, threshold }
    );
    
    // Store observer reference for cleanup
    observerRef.current = observer;
    
    // Start observing the element
    observer.observe(element);
    
    // Cleanup on unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [rootMargin, threshold, triggerOnce, disabled, isInView]);
  
  return { ref: elementRef, isInView };
}

/**
 * Hook specifically for image lazy loading with automatic srcset handling
 * 
 * @param options Configuration options for lazy loading
 * @returns Object with ref to attach to container and loaded state
 * 
 * Example usage:
 * ```tsx
 * const { ref, isLoaded } = useImageLazyLoad();
 * 
 * return (
 *   <div ref={ref}>
 *     <img 
 *       src={isLoaded ? actualSrc : placeholderSrc} 
 *       className={isLoaded ? 'opacity-100' : 'opacity-0'} 
 *     />
 *   </div>
 * );
 * ```
 */
export function useImageLazyLoad(options: UseLazyLoadOptions = {}): {
  ref: RefObject<HTMLElement>;
  isLoaded: boolean;
} {
  const { ref, isInView } = useLazyLoad({
    rootMargin: '200px', // Start loading earlier for images
    ...options,
  });
  const [isLoaded, setIsLoaded] = useState(false);
  
  useEffect(() => {
    if (isInView && !isLoaded) {
      // Use small timeout to prioritize critical resources first
      const timeoutId = setTimeout(() => {
        setIsLoaded(true);
      }, 100);
      
      return () => clearTimeout(timeoutId);
    }
  }, [isInView, isLoaded]);
  
  return { ref, isLoaded };
}

export default useLazyLoad; 