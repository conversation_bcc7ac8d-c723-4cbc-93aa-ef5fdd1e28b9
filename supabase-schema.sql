-- Rodelas Construction Services CMS Database Schema
-- This script sets up the database tables and security policies for the CMS

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) DEFAULT 'admin' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE
);

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    location VARCHAR(255) NOT NULL,
    completion_date VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    main_image VARCHAR(500) NOT NULL,
    gallery_images JSONB DEFAULT '[]'::jsonb,
    features <PERSON><PERSON><PERSON><PERSON>,
    client VARCHAR(255),
    highlights JSON<PERSON>,
    tags JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published BOOLEAN DEFAULT false
);

-- Create services table
CREATE TABLE IF NOT EXISTS services (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(100) NOT NULL,
    features JSONB NOT NULL DEFAULT '[]'::jsonb,
    benefits JSONB,
    category VARCHAR(100) NOT NULL,
    pricing_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published BOOLEAN DEFAULT false
);

-- Create company_info table
CREATE TABLE IF NOT EXISTS company_info (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    field_name VARCHAR(100) UNIQUE NOT NULL,
    field_value TEXT NOT NULL,
    field_type VARCHAR(50) DEFAULT 'text' NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(500),
    author VARCHAR(255) NOT NULL,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published BOOLEAN DEFAULT false
);

-- Create media_files table
CREATE TABLE IF NOT EXISTS media_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_category ON projects(category);
CREATE INDEX IF NOT EXISTS idx_projects_published ON projects(published);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_services_category ON services(category);
CREATE INDEX IF NOT EXISTS idx_services_published ON services(published);

CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON blog_posts(published_at DESC);

CREATE INDEX IF NOT EXISTS idx_media_files_mime_type ON media_files(mime_type);
CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_info_updated_at BEFORE UPDATE ON company_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for admin access
-- Admin users can read their own data
CREATE POLICY "Admin users can read own data" ON admin_users
    FOR SELECT USING (auth.uid()::text = id::text);

-- Only authenticated admin users can access content management tables
CREATE POLICY "Admin access to projects" ON projects
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin access to services" ON services
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin access to company_info" ON company_info
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin access to blog_posts" ON blog_posts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin access to media_files" ON media_files
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

-- Public read access for published content
CREATE POLICY "Public read access to published projects" ON projects
    FOR SELECT USING (published = true);

CREATE POLICY "Public read access to published services" ON services
    FOR SELECT USING (published = true);

CREATE POLICY "Public read access to company_info" ON company_info
    FOR SELECT USING (true);

CREATE POLICY "Public read access to published blog_posts" ON blog_posts
    FOR SELECT USING (published = true);

-- Insert some initial company information
INSERT INTO company_info (field_name, field_value, field_type) VALUES
    ('company_name', 'Rodelas Construction Services', 'text'),
    ('company_description', 'Excellence in construction, renovation, and engineering since 2010. Commercial, residential, and industrial projects.', 'text'),
    ('company_email', '<EMAIL>', 'text'),
    ('company_phone', '', 'text'),
    ('company_address', 'Philippines', 'text'),
    ('company_founded', '2010', 'text'),
    ('social_facebook', 'https://facebook.com/rodelasconstruction', 'text'),
    ('social_linkedin', 'https://linkedin.com/company/rodelas-construction-services', 'text')
ON CONFLICT (field_name) DO NOTHING;
