-- =====================================================
-- COMPLETE DATABASE FIX AND MIGRATION
-- This will backup existing data, recreate tables with proper structure, and restore data
-- =====================================================

-- 1. BACKUP EXISTING DATA
-- =====================================================

-- Create backup table for existing projects
CREATE TABLE IF NOT EXISTS projects_backup AS 
SELECT * FROM projects;

-- Show what we're backing up
SELECT 'Backed up projects:' as info;
SELECT COUNT(*) as backed_up_count FROM projects_backup;

-- 2. DROP AND RECREATE PROJECTS TABLE WITH PROPER STRUCTURE
-- =====================================================

-- Drop existing table
DROP TABLE IF EXISTS projects CASCADE;

-- Create projects table with ALL required columns
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'Residential',
    location TEXT,
    completion_date DATE,
    status TEXT DEFAULT 'completed',
    featured_image TEXT DEFAULT 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Project',
    gallery_images JSONB DEFAULT '[]'::jsonb,
    client TEXT DEFAULT '',
    is_featured BOOLEAN DEFAULT false,
    published BOOLEAN DEFAULT true,
    features JSONB DEFAULT '[]'::jsonb,
    highlights JSONB DEFAULT '[]'::jsonb,
    tags JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. RESTORE DATA FROM BACKUP (if any exists)
-- =====================================================

-- Insert backed up data with proper column mapping
INSERT INTO projects (
    id, title, description, category, location, completion_date, 
    status, featured_image, gallery_images, client, is_featured, 
    published, created_at, updated_at
)
SELECT 
    COALESCE(id::uuid, gen_random_uuid()),
    COALESCE(title, 'Untitled Project'),
    COALESCE(description, ''),
    COALESCE(category, 'Residential'),
    COALESCE(location, ''),
    COALESCE(completion_date::date, CURRENT_DATE),
    COALESCE(status, 'completed'),
    COALESCE(featured_image, 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Project'),
    COALESCE(gallery_images::jsonb, '[]'::jsonb),
    COALESCE(client, ''),
    COALESCE(is_featured, false),
    true, -- Set all restored projects as published
    COALESCE(created_at::timestamptz, NOW()),
    COALESCE(updated_at::timestamptz, NOW())
FROM projects_backup
WHERE EXISTS (SELECT 1 FROM projects_backup);

-- 4. CREATE SERVICES TABLE WITH PROPER STRUCTURE
-- =====================================================

-- Drop and recreate services table
DROP TABLE IF EXISTS services CASCADE;

CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT DEFAULT 'wrench',
    features JSONB DEFAULT '[]'::jsonb,
    benefits JSONB DEFAULT '[]'::jsonb,
    category TEXT DEFAULT 'Construction',
    pricing_info JSONB DEFAULT '{}'::jsonb,
    published BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. CREATE OTHER REQUIRED TABLES
-- =====================================================

-- Blog posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image TEXT,
    author TEXT DEFAULT 'Admin',
    published BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media files table
CREATE TABLE IF NOT EXISTS media_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    alt_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company info table
CREATE TABLE IF NOT EXISTS company_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    field_name TEXT UNIQUE NOT NULL,
    field_value TEXT NOT NULL,
    field_type TEXT DEFAULT 'text',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. FIX STORAGE BUCKET AND POLICIES
-- =====================================================

-- Create storage bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('rcs-media', 'rcs-media', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Drop existing policies
DROP POLICY IF EXISTS "Allow public uploads to rcs-media" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to rcs-media" ON storage.objects;
DROP POLICY IF EXISTS "Allow public updates to rcs-media" ON storage.objects;
DROP POLICY IF EXISTS "Allow public deletes from rcs-media" ON storage.objects;

-- Create permissive storage policies
CREATE POLICY "Allow public uploads to rcs-media"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'rcs-media');

CREATE POLICY "Allow public access to rcs-media"
ON storage.objects FOR SELECT
USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public updates to rcs-media"
ON storage.objects FOR UPDATE
USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public deletes from rcs-media"
ON storage.objects FOR DELETE
USING (bucket_id = 'rcs-media');

-- 7. VERIFY EVERYTHING
-- =====================================================

-- Show final results
SELECT 'Final projects count:' as info;
SELECT COUNT(*) as total_projects FROM projects;

SELECT 'Projects table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position;

SELECT 'Sample projects:' as info;
SELECT id, title, category, location, published, created_at 
FROM projects 
ORDER BY created_at DESC 
LIMIT 5;

-- Clean up backup table
DROP TABLE IF EXISTS projects_backup;

SELECT 'Database setup completed successfully!' as status;
