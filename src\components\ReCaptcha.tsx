import React, { forwardRef } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';

interface ReCaptchaProps {
  onChange: (token: string | null) => void;
  className?: string;
}

// Use environment variable for site key
const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

const ReCaptchaComponent = forwardRef<ReCAPTCHA, ReCaptchaProps>(({ onChange, className }, ref) => {
  return (
    <div className={`recaptcha-container ${className || ''}`}>
      <ReCAPTCHA
        ref={ref}
        sitekey={siteKey}
        onChange={onChange}
      />
    </div>
  );
});

ReCaptchaComponent.displayName = 'ReCaptcha';

export default ReCaptchaComponent; 