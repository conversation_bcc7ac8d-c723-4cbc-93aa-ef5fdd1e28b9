import { db } from '../database';

// Project data from Projects.tsx
const projectsData = [
  {
    id: "arcandsons-barbershop",
    title: "ARC & SONS Barbershop",
    category: "Commercial",
    location: "South Forbes, Silang, Cavite",
    completion_date: "2024-01-01",
    description: "Rodelas Construction Services proudly completed the full interior fit-out of ARC & SONS Barbershop, located in the stylish commercial zone of South Forbes, Silang, Cavite. We transformed this space into a modern and welcoming grooming environment that reflects the brand's identity—clean, bold, and professional.",
    featured_image: "/images/projects/commercial/arcandsons/main.webp",
    gallery_images: [
      "/images/projects/commercial/arcandsons/new1.webp",
      "/images/projects/commercial/arcandsons/new2.webp",
      "/images/projects/commercial/arcandsons/new.webp",
      "/images/projects/commercial/arcandsons/new4.webp",
      "/images/projects/commercial/arcandsons/IMG_1990.webp",
      "/images/projects/commercial/arcandsons/IMG_1991.webp",
      "/images/projects/commercial/arcandsons/IMG_1992.webp",
      "/images/projects/commercial/arcandsons/IMG_1993.webp",
      "/images/projects/commercial/arcandsons/IMG_1994.webp",
      "/images/projects/commercial/arcandsons/IMG_1995.webp",
      "/images/projects/commercial/arcandsons/IMG_1996.webp",
      "/images/projects/commercial/arcandsons/IMG_1997.webp"
    ],
    features: [
      { title: "Cabinet Fabrication", description: "Custom cabinet and counter fabrication tailored for barbershop operations" },
      { title: "Interior Carpentry", description: "Interior carpentry and woodwork with premium finishes" },
      { title: "Painting & Finishing", description: "Professional painting and finishing for a polished look" },
      { title: "Layout Design", description: "Functional layout design optimized for barbershop operations" }
    ],
    client: "ARC & SONS Barbershop",
    highlights: [
      "Modern and welcoming grooming environment",
      "Clean, bold and professional brand identity",
      "Functional workspace design",
      "Premium materials and finishes"
    ],
    tags: ["Commercial", "Interior Fit-Out", "Barbershop", "Custom Cabinetry"],
    status: "completed",
    is_featured: true
  },
  {
    id: "jezelle-fashion",
    title: "Jezelle HauteAmorado Fashion Boutique",
    category: "Commercial",
    location: "Chino Roces, Makati",
    completion_date: "2024-01-01",
    description: "We proudly present the successful completion of the Jezelle HauteAmorado Fashion Boutique, located at Chino Roces, Makati. This project involved a full commercial interior fit-out tailored to the brand identity and functionality of a fashion design business. With a clean glass façade, modern branding integration, and strategic layout design, the boutique was designed to elevate the client's professional presence while providing a functional workspace for creative expression.",
    featured_image: "/images/projects/commercial/jezelle_fashion/4.webp",
    gallery_images: [
      "/images/projects/commercial/jezelle_fashion/2.webp",
      "/images/projects/commercial/jezelle_fashion/3.webp",
      "/images/projects/commercial/jezelle_fashion/4.webp",
      "/images/projects/commercial/jezelle_fashion/5.webp",
      "/images/projects/commercial/jezelle_fashion/6.webp",
      "/images/projects/commercial/jezelle_fashion/7.webp"
    ],
    features: [
      { title: "Commercial Interior Design", description: "Full commercial space interior design and build implementation" },
      { title: "Glass Installation", description: "Glass partition installation with custom branding decals" },
      { title: "Structural Work", description: "Professional structural framing and finishing" },
      { title: "Custom Fixtures", description: "Custom lighting and flooring adjustments for optimal retail environment" },
      { title: "Display Setup", description: "Boutique display-ready preparation for immediate business use" }
    ],
    client: "Jezelle HauteAmorado",
    highlights: [
      "Modern glass façade design",
      "Brand-integrated interior layout",
      "Functional retail workspace",
      "Professional business environment"
    ],
    tags: ["Commercial", "Interior Fit-Out", "Retail Space", "Boutique"],
    status: "completed",
    is_featured: true
  },
  {
    id: "racha-project",
    title: "The Racha Project",
    category: "Residential",
    location: "Silang, Cavite",
    completion_date: "2024-01-01",
    description: "At Rodelas Construction Services, we turn your vision into reality. The Racha Project is one of our most refined residential builds — combining modern elegance with functionality. Every detail, from the structure to the finishes, was thoughtfully designed and constructed to reflect the lifestyle and comfort of its future homeowners.",
    featured_image: "/images/projects/residential/racha_project/1.webp",
    gallery_images: [
      "/images/projects/residential/racha_project/2.webp",
      "/images/projects/residential/racha_project/3.webp",
      "/images/projects/residential/racha_project/4.webp",
      "/images/projects/residential/racha_project/5.webp",
      "/images/projects/residential/racha_project/6.webp",
      "/images/projects/residential/racha_project/7.webp",
      "/images/projects/residential/racha_project/8.webp",
      "/images/projects/residential/racha_project/9.webp",
      "/images/projects/residential/racha_project/10.webp",
      "/images/projects/residential/racha_project/11.webp",
      "/images/projects/residential/racha_project/12.webp",
      "/images/projects/residential/racha_project/13.webp",
      "/images/projects/residential/racha_project/14.webp",
      "/images/projects/residential/racha_project/15.webp",
      "/images/projects/residential/racha_project/16.webp",
      "/images/projects/residential/racha_project/17.webp",
      "/images/projects/residential/racha_project/18.webp"
    ],
    features: [
      { title: "Full Residential Construction", description: "Complete turnkey construction from foundation to finishing" },
      { title: "Architectural Design", description: "Custom architectural and structural design implementation" },
      { title: "Systems Integration", description: "Comprehensive electrical and plumbing systems installation" },
      { title: "Premium Finishes", description: "High-quality interior and exterior finishing touches" }
    ],
    client: "Racha Family",
    highlights: ["Modern elegant design", "Functional living spaces", "Premium quality finishes"],
    tags: ["Residential", "Custom Home", "Modern Design", "Luxury Finishes"],
    status: "completed",
    is_featured: true
  },
  {
    id: "project-2",
    title: "Cassasis Residential Building",
    category: "Residential",
    location: "Chateux de Paris, Silang, Cavite",
    completion_date: "2024-01-01",
    description: "From Blueprints to Reality: Our Cassasis Residential Building at Chateux de Paris, Silang, Cavite is finished. Our sincere gratitude for Cassasis Family for trusting the ability and craftmanship of Rodelas Construction Services, where form meets function in a masterpiece built to endure. Glory to God!",
    featured_image: "/images/projects/residential/cassasis_residential/main.webp",
    gallery_images: [
      "/images/projects/residential/cassasis_residential/gallery1.webp",
      "/images/projects/residential/cassasis_residential/gallery2.webp",
      "/images/projects/residential/cassasis_residential/gallery3.webp"
    ],
    features: [
      { title: "Custom Finishes", description: "Premium materials and craftsmanship throughout" },
      { title: "Architectural Excellence", description: "Unique design that balances aesthetics and functionality" },
      { title: "Energy Efficiency", description: "Smart home features for optimal energy consumption" }
    ],
    client: "Cassasis Family",
    highlights: ["Turnkey project delivery", "Custom design elements", "Satisfied homeowner testimonial"],
    tags: ["Residential", "Custom Home", "Luxury Finishes", "Modern Design"],
    status: "completed",
    is_featured: false
  },
  {
    id: "ayala-alabang-residential",
    title: "Ayala Alabang – 2-Storey Residential Build",
    category: "Residential",
    location: "Ayala Alabang",
    completion_date: "2021-01-01",
    description: "We're proud to share the completion of a two-storey residential building located in the prestigious Ayala, Alabang area. This project combined elegant design with functionality, featuring high-end finishes and modern interiors that reflect the client's refined taste.",
    featured_image: "/images/projects/residential/ayala/a.webp",
    gallery_images: [
      "/images/projects/residential/ayala/b.webp",
      "/images/projects/residential/ayala/c.webp",
      "/images/projects/residential/ayala/d.webp",
      "/images/projects/residential/ayala/e.webp",
      "/images/projects/residential/ayala/f.webp",
      "/images/projects/residential/ayala/g.webp",
      "/images/projects/residential/ayala/h.webp",
      "/images/projects/residential/ayala/i.webp",
      "/images/projects/residential/ayala/j.webp"
    ],
    features: [
      { title: "Full Construction", description: "Full construction and finishing of a 2-storey residence" },
      { title: "Custom Cabinetry", description: "Custom cabinetry and lighting throughout the home" },
      { title: "Modern Installations", description: "Modern kitchen and bathroom installations" },
      { title: "Ceiling Treatments", description: "Elegant ceiling treatments and lighting fixtures" },
      { title: "Turnkey Completion", description: "Turnover-ready completion with attention to detail" }
    ],
    client: "Ayala Alabang Residence",
    highlights: [
      "Luxurious and livable home design",
      "High-end finishes and materials",
      "Architectural excellence",
      "Interior styling and detailing"
    ],
    tags: ["Residential", "High-End Residential", "New Build", "Luxury Home"],
    status: "completed",
    is_featured: false
  },
  {
    id: "south-forbes-villas",
    title: "South Forbes Villas",
    category: "Renovation",
    location: "South Forbes Villas",
    completion_date: "2024-01-01",
    description: "We've successfully completed a full residential renovation and repaint project in the elegant community of South Forbes Villas. This project focused on refreshing the exterior and interior of the home, giving it a clean, modern, and revitalized look while maintaining its classic architectural character. Careful attention was given to surface preparation, paint application, and detail finishing to ensure high-quality results and long-term durability.",
    featured_image: "/images/projects/renovation/southforbes/1.webp",
    gallery_images: [
      "/images/projects/renovation/southforbes/2.webp",
      "/images/projects/renovation/southforbes/3.webp",
      "/images/projects/renovation/southforbes/4.webp",
      "/images/projects/renovation/southforbes/5.webp",
      "/images/projects/renovation/southforbes/6.webp",
      "/images/projects/renovation/southforbes/7.webp",
      "/images/projects/renovation/southforbes/8.webp"
    ],
    features: [
      { title: "Complete Repainting", description: "Full interior and exterior repainting service" },
      { title: "Surface Restoration", description: "Thorough surface preparation and repair work" },
      { title: "Detail Finishing", description: "Meticulous work on trims and moldings" },
      { title: "Interior Enhancement", description: "Comprehensive ceiling and wall touch-ups" },
      { title: "General Refurbishment", description: "Overall interior improvement and renovation" }
    ],
    client: "South Forbes Villas Residence",
    highlights: [
      "Modern and revitalized look",
      "High-quality paint application",
      "Long-term durability focus",
      "Classic character preservation"
    ],
    tags: ["Renovation", "Residential", "Home Improvement", "Repainting"],
    status: "completed",
    is_featured: false
  }
];

export async function migrateProjects() {
  console.log('Starting project migration...');
  
  try {
    for (const project of projectsData) {
      console.log(`Migrating project: ${project.title}`);
      
      // Insert project (let database generate UUID, don't use string ID)
      const insertedProject = await db.projects.create({
        title: project.title,
        description: project.description,
        category: project.category,
        location: project.location,
        completion_date: project.completion_date,
        featured_image: project.featured_image,
        gallery_images: JSON.stringify(project.gallery_images),
        client: project.client,
        status: project.status,
        is_featured: project.is_featured,
        tags: project.tags,
        features: project.features,
        highlights: project.highlights,
        published: true
      });
      
      console.log(`✅ Successfully migrated: ${project.title}`);
    }
    
    console.log('✅ All projects migrated successfully!');
    return { success: true, message: 'Projects migrated successfully' };
  } catch (error) {
    console.error('❌ Error migrating projects:', error);
    return { success: false, error };
  }
}
