# Set default behavior to automatically normalize line endings
* text=auto

# Declare files that will always have LF line endings on checkout
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.html text eol=lf
*.svg text eol=lf
*.yml text eol=lf
*.yaml text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.otf binary
*.mp4 binary
*.webm binary
*.webp binary

# Exclude .env files from git diff to avoid showing sensitive data
.env linguist-generated
.env.* linguist-generated 