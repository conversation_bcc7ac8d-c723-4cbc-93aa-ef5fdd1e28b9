import React, { useState, useEffect } from 'react';
import { Message } from './types';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import { cn } from '@/lib/utils';
import { PlusCircle, MessageSquare, Calculator, Calendar, Building, Home, Wrench, Phone } from 'lucide-react';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  onButtonClick: (action: string, path?: string) => void;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  className?: string;
}

// Predefined conversation starters
const conversationStarters = [
  { text: "Get a cost estimate", action: "I need a cost estimate", icon: <Calculator size={14} /> },
  { text: "Schedule a consultation", action: "I want to schedule a consultation", icon: <Calendar size={14} /> },
  { text: "Commercial projects", action: "Tell me about commercial construction", icon: <Building size={14} /> },
  { text: "Residential services", action: "What residential services do you offer?", icon: <Home size={14} /> },
  { text: "Renovation options", action: "I'm interested in renovation", icon: <Wrench size={14} /> }
];

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  onButtonClick,
  messagesEndRef,
  className
}) => {
  const [showStarters, setShowStarters] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);

  // Toggle initial conversation starters
  const toggleStarters = () => {
    setShowStarters(prev => !prev);
  };

  // Toggle more options at bottom
  const toggleMoreOptions = () => {
    setShowMoreOptions(prev => !prev);
  };

  // Ensure messages scroll into view when new ones arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isLoading, messagesEndRef]);

  return (
    <div 
      className={cn(
        "flex-1 p-4 overflow-y-auto overflow-x-hidden overscroll-contain pb-safe", 
        className
      )}
      style={{ 
        WebkitOverflowScrolling: 'touch',
        paddingBottom: 'calc(1rem + env(safe-area-inset-bottom, 0px))'
      }}
    >
      {messages.length === 0 ? (
        <div className="h-full flex flex-col items-center justify-center">
          <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4 shadow-md">
            <MessageSquare className="h-8 w-8 text-rcs-blue" />
          </div>
          <h3 className="text-xl font-semibold mb-2 text-center text-gray-800">Welcome to RCS Chat</h3>
          <p className="text-gray-500 text-center text-sm md:text-base max-w-md mb-6">
            Ask me about our construction services, projects, or how to get a quote.
          </p>
          
          <div className="grid grid-cols-2 gap-3 w-full max-w-sm">
            <SuggestionButton 
              icon={<Building className="w-4 h-4 mr-2" />}
              label="Services"
              onClick={() => onButtonClick('services')}
            />
            <SuggestionButton 
              icon={<Home className="w-4 h-4 mr-2" />}
              label="Projects"
              onClick={() => onButtonClick('projects')}
            />
            <SuggestionButton 
              icon={<Wrench className="w-4 h-4 mr-2" />}
              label="About Us"
              onClick={() => onButtonClick('about')}
            />
            <SuggestionButton 
              icon={<Phone className="w-4 h-4 mr-2" />}
              label="Contact"
              onClick={() => onButtonClick('contact')}
            />
          </div>
        </div>
      ) : (
        <div className="space-y-4 pb-2 pt-1">
          {/* Welcome message for new conversations */}
          {messages.length === 1 && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-100 text-sm text-blue-700">
              <p className="font-medium mb-2">Welcome to RCS Assistant</p>
              <p className="text-xs mb-2">Try asking about our services or use a quick action below:</p>
              <div className="flex flex-wrap gap-2 mt-2">
                <button 
                  onClick={toggleStarters}
                  className="flex items-center gap-1 px-2 py-1 rounded-full bg-white text-blue-600 text-xs border border-blue-200 hover:bg-blue-50"
                >
                  <PlusCircle size={12} /> Show options
                </button>
              </div>
            </div>
          )}
          
          {/* Initial conversation starters panel */}
          {showStarters && messages.length === 1 && (
            <div className="mb-4 p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
              <p className="text-xs text-gray-500 mb-2">Conversation starters:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {conversationStarters.map((starter, i) => (
                  <button 
                    key={i}
                    onClick={() => {
                      onButtonClick(starter.action);
                      setShowStarters(false);
                    }}
                    className="flex items-center gap-2 px-3 py-2 rounded-md bg-gray-50 hover:bg-gray-100 text-sm text-gray-700 text-left transition-colors"
                  >
                    <span className="text-blue-500">{starter.icon}</span>
                    {starter.text}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {/* Message list */}
          <div className="space-y-2 md:space-y-3">
            {messages.map((message, index) => (
              <ChatMessage 
                key={index} 
                message={message}
                onButtonClick={onButtonClick}
              />
            ))}
          </div>
          
          {/* Loading indicator */}
          {isLoading && (
            <div className="flex items-start mt-3">
              <TypingIndicator />
            </div>
          )}
          
          {/* More options button at the bottom */}
          {messages.length > 1 && !isLoading && (
            <div className="flex justify-center my-4 gap-2">
              <button
                onClick={toggleMoreOptions}
                className="flex items-center gap-1 px-2.5 py-1.5 rounded-full bg-gray-50 text-gray-500 text-xs hover:bg-gray-100"
              >
                <MessageSquare size={12} /> {showMoreOptions ? 'Hide options' : 'More options'}
              </button>
            </div>
          )}
          
          {/* More options panel at the bottom */}
          {showMoreOptions && messages.length > 1 && (
            <div className="mt-2 mb-4 p-3 bg-white rounded-lg border border-gray-200 shadow-sm animate-fadeIn">
              <p className="text-xs text-gray-500 mb-2">Quick actions:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {conversationStarters.map((starter, i) => (
                  <button 
                    key={i}
                    onClick={() => {
                      onButtonClick(starter.action);
                      setShowMoreOptions(false);
                    }}
                    className="flex items-center gap-2 px-3 py-2 rounded-md bg-gray-50 hover:bg-gray-100 text-sm text-gray-700 text-left transition-colors"
                  >
                    <span className="text-blue-500">{starter.icon}</span>
                    {starter.text}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} className="h-1" aria-hidden="true" />
        </div>
      )}
    </div>
  );
};

// Suggestion button component for empty state
const SuggestionButton = ({ icon, label, onClick }: { icon: React.ReactNode, label: string, onClick: () => void }) => (
  <button 
    className="flex items-center justify-center bg-white border border-gray-200 rounded-xl p-3 text-sm text-gray-700 hover:bg-gray-50 hover:border-blue-200 transition-all duration-300 shadow-sm hover:shadow"
    onClick={onClick}
  >
    {icon}
    {label}
  </button>
);

export default ChatContainer; 