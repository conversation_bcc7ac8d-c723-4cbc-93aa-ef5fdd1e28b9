import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { NavLink, Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu as MenuIcon, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Menu as NavMenu, MenuItem, ServiceItem, HoveredLink } from '@/components/ui/navbar-menu';
import { OptimizedImage } from '@/lib/imageUtils';
import { motion, AnimatePresence } from 'framer-motion';

// Debounce function to limit how often a function is called
const debounce = (func: Function, wait: number) => {
  let timeout: number | null = null;
  
  return (...args: any[]) => {
    if (timeout) {
      window.clearTimeout(timeout);
    }
    timeout = window.setTimeout(() => {
      func(...args);
    }, wait);
  };
};

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const navbarRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<number | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    setActiveItem(null);
    if (closeTimeoutRef.current) {
      window.clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  }, [location.pathname]);

  useEffect(() => {
    // Create debounced scroll handler for better performance
    const handleScroll = debounce(() => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    }, 10);

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen(prev => !prev);
  }, []);

  // Define handleClickOutside outside of useEffect
  const handleClickOutside = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (
      activeItem !== null && 
      !target.closest('.navbar-menu-area') && 
      !target.closest('.dropdown-content')
    ) {
      setActiveItem(null);
      if (closeTimeoutRef.current) {
        window.clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
    }
  }, [activeItem]);

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [handleClickOutside]);

  // Define these handlers outside of useEffect
  const handleMouseLeave = useCallback((event: MouseEvent) => {
    if (navbarRef.current && !navbarRef.current.contains(event.relatedTarget as Node)) {
      closeTimeoutRef.current = window.setTimeout(() => {
        setActiveItem(null);
      }, 200); // Reduced timeout
    }
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (closeTimeoutRef.current) {
      window.clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    const navbarElement = navbarRef.current;
    navbarElement?.addEventListener('mouseleave', handleMouseLeave as EventListener);
    navbarElement?.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      navbarElement?.removeEventListener('mouseleave', handleMouseLeave as EventListener);
      navbarElement?.removeEventListener('mouseenter', handleMouseEnter);
      if (closeTimeoutRef.current) {
        window.clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [handleMouseLeave, handleMouseEnter]);

  const handleNavigation = useCallback((path: string) => {
    setActiveItem(null);
    navigate(path);
  }, [navigate]);

  // Memoize the navbar classes to prevent unnecessary re-calculations
  const navbarClasses = useMemo(() => 
    cn(
      'fixed w-full z-[999] transition-all duration-300',
      isScrolled 
        ? 'bg-white/100 shadow-lg py-2 backdrop-blur-md' 
        : 'bg-transparent py-4'
    ), 
    [isScrolled]
  );

  const containerClasses = useMemo(() => 
    cn(
      "container mx-auto px-4 md:px-6",
      isScrolled ? 'text-rcs-blue' : 'text-white'
    ),
    [isScrolled]
  );

  const logoTextClasses = useMemo(() => 
    cn(
      "font-montserrat font-bold text-2xl",
      isScrolled ? 'text-rcs-blue' : 'text-white'
    ),
    [isScrolled]
  );

  const subtitleClasses = useMemo(() => 
    cn(
      "font-montserrat font-medium text-sm",
      isScrolled ? 'text-rcs-gold' : 'text-white'
    ),
    [isScrolled]
  );

  const menuButtonClasses = useMemo(() => 
    cn(
      "md:hidden",
      isScrolled ? 'text-rcs-blue' : 'text-white'
    ),
    [isScrolled]
  );

  return (
    <nav
      className={navbarClasses}
      ref={navbarRef}
    >
      <div className={containerClasses}>
        <div className="flex justify-between items-center">
          <NavLink to="/" className="flex items-center">
            <OptimizedImage 
              src="/rcslogo.jpg" 
              alt="RCS Logo" 
              className="w-12 h-12 rounded-full object-cover mr-3"
              loading="lazy"
            />
            <div className="flex flex-col">
              <span className={logoTextClasses}>RCS</span>
              <span className={subtitleClasses}>
                Rodelas Construction Services
              </span>
            </div>
          </NavLink>

          <div className="hidden md:flex navbar-menu-area">
            <NavMenu 
              setActive={setActiveItem} 
              className={cn(
                "bg-transparent border-0 shadow-none py-0",
                isScrolled ? "text-rcs-blue" : "text-white"
              )}
            >
              <MenuItem 
                setActive={setActiveItem} 
                active={activeItem} 
                item="Home"
                to="/"
              >
                <div className="flex flex-col space-y-4 text-sm min-w-[200px]">
                  <HoveredLink to="/#home">Homepage</HoveredLink>
                  <HoveredLink to="/#about">About Us</HoveredLink>
                  <HoveredLink to="/#testimonials">Testimonials</HoveredLink>
                  <HoveredLink to="/#contact">Get in Touch</HoveredLink>
                </div>
              </MenuItem>
              
              <MenuItem 
                setActive={setActiveItem} 
                active={activeItem} 
                item="Services"
                to="/services"
              >
                <div className="flex flex-col space-y-4 text-sm min-w-[200px]">
                  <HoveredLink to="/services#all">All Services</HoveredLink>
                  <HoveredLink to="/services#commercial">Commercial</HoveredLink>
                  <HoveredLink to="/services#residential">Residential</HoveredLink>
                  <HoveredLink to="/services#specialized">Specialized Services</HoveredLink>
                  <HoveredLink to="/services#management">Management & Planning</HoveredLink>
                </div>
              </MenuItem>
              
              <MenuItem 
                setActive={setActiveItem} 
                active={activeItem} 
                item="Projects"
                to="/projects"
              >
                <div className="grid grid-cols-2 gap-6 p-2 text-sm min-w-[500px]">
                  <ServiceItem
                    title="All Projects"
                    href="/projects#All"
                    src="/images/projects/residential/ayala/a.webp"
                    description="View our complete portfolio of projects"
                  />
                  <ServiceItem
                    title="Commercial Projects"
                    href="/projects#Commercial"
                    src="/images/projects/commercial/jezelle_fashion/1.webp"
                    description="Office spaces, retail and commercial buildings"
                  />
                  <ServiceItem
                    title="Residential Projects"
                    href="/projects#Residential"
                    src="/images/projects/residential/ayala/h.webp"
                    description="Custom homes and modern residential developments"
                  />
                  <ServiceItem
                    title="Renovation Projects"
                    href="/projects#Renovation"
                    src="/images/projects/renovation/southforbes/1.webp"
                    description="Transforming existing spaces with expertise"
                  />
                </div>
              </MenuItem>
              
              <MenuItem 
                setActive={setActiveItem} 
                active={activeItem} 
                item="Contact"
                to="/contact"
              >
                <div className="flex flex-col space-y-4 text-sm min-w-[200px]">
                  <HoveredLink to="/contact#form">Contact Form</HoveredLink>
                  <HoveredLink to="/contact#location">Office Location</HoveredLink>
                </div>
              </MenuItem>
            </NavMenu>
          </div>

          <motion.button 
            className={menuButtonClasses} 
            onClick={toggleMenu} 
            aria-label="Toggle mobile menu"
            initial={false}
            animate={isMenuOpen ? "open" : "closed"}
          >
            <div className="relative w-6 h-6 flex items-center justify-center">
              <motion.span 
                className="absolute w-5 h-0.5 bg-current rounded-full"
                variants={{
                  open: { rotate: 45, y: 0 },
                  closed: { rotate: 0, y: -6 }
                }}
                transition={{ duration: 0.3 }}
              />
              <motion.span 
                className="absolute w-5 h-0.5 bg-current rounded-full"
                variants={{
                  open: { opacity: 0 },
                  closed: { opacity: 1 }
                }}
                transition={{ duration: 0.3 }}
              />
              <motion.span 
                className="absolute w-5 h-0.5 bg-current rounded-full"
                variants={{
                  open: { rotate: -45, y: 0 },
                  closed: { rotate: 0, y: 6 }
                }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </motion.button>
        </div>

        <AnimatePresence>
          {isMenuOpen && (
            <motion.div 
              className="md:hidden bg-white absolute top-full left-0 w-full shadow-md mobile-menu z-50"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="container mx-auto px-4 py-6 flex flex-col space-y-5">
                <motion.div
                  className="flex flex-col items-center justify-center space-y-4 text-lg"
                  variants={{
                    open: {
                      transition: {
                        staggerChildren: 0.07,
                        delayChildren: 0.1
                      }
                    },
                    closed: {
                      transition: {
                        staggerChildren: 0.05,
                        staggerDirection: -1
                      }
                    }
                  }}
                  initial="closed"
                  animate="open"
                >
                  <motion.div
                    variants={{
                      open: { y: 0, opacity: 1 },
                      closed: { y: -20, opacity: 0 }
                    }}
                    transition={{ duration: 0.4 }}
                  >
                    <NavLink
                      to="/"
                      className={({ isActive }) =>
                        cn(
                          'font-montserrat font-medium py-2 transition-colors duration-200 block text-center',
                          isActive ? 'text-rcs-gold' : 'text-rcs-blue hover:text-rcs-gold'
                        )
                      }
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Home
                    </NavLink>
                  </motion.div>
                  
                  <motion.div
                    variants={{
                      open: { y: 0, opacity: 1 },
                      closed: { y: -20, opacity: 0 }
                    }}
                    transition={{ duration: 0.4 }}
                  >
                    <NavLink
                      to="/services"
                      className={({ isActive }) =>
                        cn(
                          'font-montserrat font-medium py-2 transition-colors duration-200 block text-center',
                          isActive ? 'text-rcs-gold' : 'text-rcs-blue hover:text-rcs-gold'
                        )
                      }
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Services
                    </NavLink>
                  </motion.div>
                  
                  <motion.div
                    variants={{
                      open: { y: 0, opacity: 1 },
                      closed: { y: -20, opacity: 0 }
                    }}
                    transition={{ duration: 0.4 }}
                  >
                    <NavLink
                      to="/projects"
                      className={({ isActive }) =>
                        cn(
                          'font-montserrat font-medium py-2 transition-colors duration-200 block text-center',
                          isActive ? 'text-rcs-gold' : 'text-rcs-blue hover:text-rcs-gold'
                        )
                      }
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Projects
                    </NavLink>
                  </motion.div>
                  
                  <motion.div
                    variants={{
                      open: { y: 0, opacity: 1 },
                      closed: { y: -20, opacity: 0 }
                    }}
                    transition={{ duration: 0.4 }}
                  >
                    <NavLink
                      to="/contact"
                      className={({ isActive }) =>
                        cn(
                          'font-montserrat font-medium py-2 transition-colors duration-200 block text-center',
                          isActive ? 'text-rcs-gold' : 'text-rcs-blue hover:text-rcs-gold'
                        )
                      }
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Contact
                    </NavLink>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );
};

export default Navbar;
