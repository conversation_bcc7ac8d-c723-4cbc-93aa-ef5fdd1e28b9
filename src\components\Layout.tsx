import React, { useEffect } from 'react';
import Navbar from './Navbar';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  // Organization schema for structured data
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Rodelas Construction Services",
    "url": "https://rodelascons.com",
    "logo": "https://rodelascons.com/rcslogo.webp",
    "description": "Excellence in construction, renovation, and engineering since 2010. Commercial, residential, and industrial projects.",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "areaServed": "Philippines"
    },
    "sameAs": [
      "https://facebook.com/rodelasconstruction",
      "https://linkedin.com/company/rodelas-construction-services"
    ]
  };

  // Local business schema
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "GeneralContractor",
    "name": "Rodelas Construction Services",
    "image": "https://rodelascons.com/rcslogo.webp",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Philippines"
    },
    "priceRange": "$$",
    "openingHours": "Mo-Fr 08:00-17:00"
  };

  // Add structured data to the document head
  useEffect(() => {
    // Create script elements for each schema
    const orgScript = document.createElement('script');
    orgScript.type = 'application/ld+json';
    orgScript.textContent = JSON.stringify(organizationSchema);
    
    const businessScript = document.createElement('script');
    businessScript.type = 'application/ld+json';
    businessScript.textContent = JSON.stringify(localBusinessSchema);
    
    // Add scripts to head
    document.head.appendChild(orgScript);
    document.head.appendChild(businessScript);
    
    // Cleanup on unmount
    return () => {
      document.head.removeChild(orgScript);
      document.head.removeChild(businessScript);
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
};

export default Layout;
