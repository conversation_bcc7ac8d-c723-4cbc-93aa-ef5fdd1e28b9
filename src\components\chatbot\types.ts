export interface Message {
  content: string;
  isBot: boolean;
  timestamp: Date;
  buttons?: Array<{
    text: string;
    action: string;
    path?: string;
  }>;
  
  // New fields for structured content
  sections?: {
    title: string;
    content: string;
    icon?: string;
  }[];
  formattedContent?: boolean; // Indicates if the content should be formatted specially
}

// Define a type for conversation context
export interface ConversationContext {
  userName?: string;
  projectType?: string;
  projectSize?: string;
  budget?: string;
  location?: string;
  preferredFinishType?: string;
  contactPreference?: string;
  lastDiscussedService?: string;
  
  // Contact form collection fields
  collectingContact?: boolean;
  contactStage?: 'name' | 'email' | 'phone' | 'message' | null;
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
}

export interface Conversation {
  id: string;
  title: string;
  timestamp: Date;
  messages: Message[];
} 