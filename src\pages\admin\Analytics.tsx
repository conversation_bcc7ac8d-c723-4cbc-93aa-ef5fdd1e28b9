import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, FileText, Image, Calendar } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { db } from '@/lib/database';
import type { Project, Service, BlogPost, MediaFile } from '@/lib/database.types';

interface AnalyticsData {
  projects: {
    total: number;
    byCategory: Record<string, number>;
    byStatus: Record<string, number>;
    featured: number;
    recentlyAdded: number;
  };
  services: {
    total: number;
    byCategory: Record<string, number>;
    featured: number;
  };
  blog: {
    total: number;
    published: number;
    drafts: number;
    recentlyPublished: number;
  };
  media: {
    total: number;
    totalSize: number;
    byType: Record<string, number>;
    recentlyUploaded: number;
  };
}

const AnalyticsAdmin = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Fetch all data
      const [projects, services, blogPosts, mediaFiles] = await Promise.all([
        db.projects.getAll(),
        db.services.getAll(),
        db.blogPosts.getAll(),
        db.mediaFiles.getAll()
      ]);

      // Calculate date threshold for "recent" items
      const daysAgo = parseInt(timeRange);
      const threshold = new Date();
      threshold.setDate(threshold.getDate() - daysAgo);

      // Process projects analytics
      const projectsByCategory = projects.reduce((acc, project) => {
        acc[project.category] = (acc[project.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const projectsByStatus = projects.reduce((acc, project) => {
        acc[project.status] = (acc[project.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const recentProjects = projects.filter(p => 
        new Date(p.created_at) > threshold
      ).length;

      // Process services analytics
      const servicesByCategory = services.reduce((acc, service) => {
        acc[service.category] = (acc[service.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Process blog analytics
      const publishedPosts = blogPosts.filter(post => post.status === 'published');
      const draftPosts = blogPosts.filter(post => post.status === 'draft');
      const recentlyPublishedPosts = publishedPosts.filter(post => 
        post.published_at && new Date(post.published_at) > threshold
      ).length;

      // Process media analytics
      const mediaByType = mediaFiles.reduce((acc, file) => {
        const type = file.mime_type?.split('/')[0] || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const totalMediaSize = mediaFiles.reduce((acc, file) => 
        acc + (file.file_size || 0), 0
      );

      const recentMedia = mediaFiles.filter(file => 
        new Date(file.created_at) > threshold
      ).length;

      setAnalytics({
        projects: {
          total: projects.length,
          byCategory: projectsByCategory,
          byStatus: projectsByStatus,
          featured: projects.filter(p => p.is_featured).length,
          recentlyAdded: recentProjects
        },
        services: {
          total: services.length,
          byCategory: servicesByCategory,
          featured: services.filter(s => s.is_featured).length
        },
        blog: {
          total: blogPosts.length,
          published: publishedPosts.length,
          drafts: draftPosts.length,
          recentlyPublished: recentlyPublishedPosts
        },
        media: {
          total: mediaFiles.length,
          totalSize: totalMediaSize,
          byType: mediaByType,
          recentlyUploaded: recentMedia
        }
      });

    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load analytics data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Overview of your CMS content and activity</p>
        </div>
        
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.projects.total}</div>
            <p className="text-xs text-muted-foreground">
              +{analytics.projects.recentlyAdded} in last {timeRange} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.services.total}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.services.featured} featured services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.blog.total}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.blog.published} published, {analytics.blog.drafts} drafts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Media Files</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.media.total}</div>
            <p className="text-xs text-muted-foreground">
              {formatFileSize(analytics.media.totalSize)} total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Projects by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Projects by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.projects.byCategory).map(([category, count]) => (
                <div key={category} className="flex justify-between items-center">
                  <span className="capitalize">{category}</span>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Projects by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Projects by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.projects.byStatus).map(([status, count]) => (
                <div key={status} className="flex justify-between items-center">
                  <span className="capitalize">{status.replace('_', ' ')}</span>
                  <Badge variant={status === 'completed' ? 'default' : 'secondary'}>
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Services by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Services by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.services.byCategory).map(([category, count]) => (
                <div key={category} className="flex justify-between items-center">
                  <span className="capitalize">{category}</span>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Media by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Media Files by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.media.byType).map(([type, count]) => (
                <div key={type} className="flex justify-between items-center">
                  <span className="capitalize">{type}</span>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Recent Activity (Last {timeRange} days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analytics.projects.recentlyAdded}
              </div>
              <p className="text-sm text-gray-600">New Projects</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analytics.blog.recentlyPublished}
              </div>
              <p className="text-sm text-gray-600">Published Posts</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {analytics.media.recentlyUploaded}
              </div>
              <p className="text-sm text-gray-600">Uploaded Files</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {analytics.projects.featured + analytics.services.featured}
              </div>
              <p className="text-sm text-gray-600">Featured Items</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsAdmin;
