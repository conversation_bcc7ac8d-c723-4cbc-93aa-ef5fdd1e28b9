import React from 'react';
import HeroSection from '../components/home/<USER>';
import AboutUsSection from '../components/home/<USER>';
import ServicesSection from '../components/home/<USER>';
import WhyChooseUs from '../components/home/<USER>';
import ProjectShowcase from '../components/home/<USER>';
import TestimonialsSection from '../components/home/<USER>';
import TeamSection from '../components/home/<USER>';
import FaqSection from '../components/home/<USER>';
import CtaSection from '../components/home/<USER>';
import { motion } from 'framer-motion';
import { useEffect } from 'react';

const Index = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    
    // Handle hash navigation on page load
    const hash = window.location.hash.slice(1);
    if (hash) {
      setTimeout(() => {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 500);
    }
  }, []);
  
  // Page transition variants
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.6, ease: "easeOut" } },
    exit: { opacity: 0, transition: { duration: 0.4 } }
  };
  
  // Staggered content sections
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  
  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };
  
  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
    >
      <div id="home">
        <HeroSection />
      </div>
      
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={sectionVariants} id="about">
          <AboutUsSection />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="services">
          <ServicesSection />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="why-choose-us">
          <WhyChooseUs />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="projects">
          <ProjectShowcase />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="team">
          <TeamSection />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="testimonials">
          <TestimonialsSection />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="faq">
          <FaqSection />
        </motion.div>
        
        <motion.div variants={sectionVariants} id="contact">
          <CtaSection />
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default Index;
