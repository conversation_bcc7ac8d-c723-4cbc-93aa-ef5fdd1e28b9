import { supabase } from './supabase';

export interface UploadResult {
  url: string;
  path: string;
  error?: string;
}

export class SimpleStorageService {
  private static readonly BUCKET_NAME = 'rcs-media';
  
  /**
   * Upload a file to Supabase Storage (simplified version)
   */
  static async uploadFile(
    file: File, 
    folder: 'projects' | 'services' | 'blog' | 'media' = 'media',
    customName?: string
  ): Promise<UploadResult> {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        return {
          url: '',
          path: '',
          error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.'
        };
      }
      
      // Validate file size (10MB max)
      if (file.size > 10485760) {
        return {
          url: '',
          path: '',
          error: 'File size too large. Maximum size is 10MB.'
        };
      }
      
      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const fileExtension = file.name.split('.').pop();
      const fileName = customName 
        ? `${customName}-${timestamp}.${fileExtension}`
        : `${timestamp}-${randomString}.${fileExtension}`;
      
      const filePath = `${folder}/${fileName}`;
      
      // Upload file with proper headers
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          duplex: 'half'
        });
      
      if (error) {
        console.error('Upload error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error.error,
          details: error
        });
        return {
          url: '',
          path: '',
          error: `Upload failed: ${error.message || error.error || 'Unknown error'}`
        };
      }
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);
      
      return {
        url: urlData.publicUrl,
        path: filePath,
        error: undefined
      };
      
    } catch (error) {
      console.error('Storage service error:', error);
      return {
        url: '',
        path: '',
        error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
  
  /**
   * Upload multiple files
   */
  static async uploadMultipleFiles(
    files: File[],
    folder: 'projects' | 'services' | 'blog' | 'media' = 'media'
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(file => this.uploadFile(file, folder));
    return Promise.all(uploadPromises);
  }
  
  /**
   * Delete a file from storage
   */
  static async deleteFile(filePath: string): Promise<boolean> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);
      
      if (error) {
        console.error('Delete error:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Storage delete error:', error);
      return false;
    }
  }
  
  /**
   * Get public URL for a file
   */
  static getPublicUrl(filePath: string): string {
    const { data } = supabase.storage
      .from(this.BUCKET_NAME)
      .getPublicUrl(filePath);
    
    return data.publicUrl;
  }
  
  /**
   * List files in a folder
   */
  static async listFiles(folder: string = '') {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(folder);
      
      if (error) {
        console.error('List files error:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Storage list error:', error);
      return [];
    }
  }
}
