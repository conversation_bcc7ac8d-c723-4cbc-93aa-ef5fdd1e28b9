import { ConversationContext } from './types';

// Extract context from user messages
export const extractContextFromMessage = (message: string): Partial<ConversationContext> => {
  const contextUpdates: Partial<ConversationContext> = {};
  
  // Extract name
  const nameMatch = message.match(/(?:my name is|i am|i'm|call me) ([\w\s]+)/i);
  if (nameMatch && nameMatch[1]) {
    contextUpdates.userName = nameMatch[1].trim();
  }
  
  // Extract project type
  if (message.toLowerCase().includes('residential') || message.toLowerCase().includes('house') || 
      message.toLowerCase().includes('home') || message.toLowerCase().includes('bahay')) {
    contextUpdates.projectType = 'residential';
    contextUpdates.lastDiscussedService = 'Residential Construction';
  } else if (message.toLowerCase().includes('commercial') || message.toLowerCase().includes('office') || 
              message.toLowerCase().includes('building') || message.toLowerCase().includes('store')) {
    contextUpdates.projectType = 'commercial';
    contextUpdates.lastDiscussedService = 'Commercial Construction';
  } else if (message.toLowerCase().includes('renovation') || message.toLowerCase().includes('remodel') || 
              message.toLowerCase().includes('renovate') || message.toLowerCase().includes('update')) {
    contextUpdates.projectType = 'renovation';
    contextUpdates.lastDiscussedService = 'Renovation & Remodeling';
  }
  
  // Extract project size
  const sizeMatch = message.match(/(\d+)\s*(?:sqm|square meter|sq\.m|square m|sq m)/i);
  if (sizeMatch && sizeMatch[1]) {
    contextUpdates.projectSize = sizeMatch[1];
  }
  
  // Extract budget or finish type preferences
  if (message.toLowerCase().includes('premium') || message.toLowerCase().includes('high end') || 
      message.toLowerCase().includes('luxury')) {
    contextUpdates.preferredFinishType = 'premium';
  } else if (message.toLowerCase().includes('standard') || message.toLowerCase().includes('mid range')) {
    contextUpdates.preferredFinishType = 'standard';
  } else if (message.toLowerCase().includes('basic') || message.toLowerCase().includes('simple') || 
              message.toLowerCase().includes('affordable')) {
    contextUpdates.preferredFinishType = 'basic';
  }
  
  // Extract location
  const locationPatterns = [
    /(?:in|at|near|from)\s+([\w\s,]+?)(?:\.|,|$)/i,
    /(?:location is|located in|area is)\s+([\w\s,]+?)(?:\.|,|$)/i
  ];
  
  for (const pattern of locationPatterns) {
    const match = message.match(pattern);
    if (match && match[1] && match[1].length > 3) { // Avoid very short matches
      contextUpdates.location = match[1].trim();
      break;
    }
  }
  
  // Extract contact preference
  if (message.toLowerCase().includes('call') || message.toLowerCase().includes('phone') || 
      message.toLowerCase().includes('tawag')) {
    contextUpdates.contactPreference = 'phone';
  } else if (message.toLowerCase().includes('email') || message.toLowerCase().includes('message') || 
              message.toLowerCase().includes('text')) {
    contextUpdates.contactPreference = 'email';
  }
  
  return contextUpdates;
};

// Process calculation for cost estimates
export const processCalculation = (query: string) => {
  const numberMatch = query.match(/\d+/);
  const sqm = numberMatch ? parseInt(numberMatch[0]) : null;
  
  if (!sqm) return null;
  
  let baseRate = 0;
  let projectType = "";
  let finishType = "standard";
  
  // Determine project type
  if (query.toLowerCase().includes('residential')) {
    projectType = "residential construction";
    if (query.toLowerCase().includes('premium')) {
      baseRate = 65000;
      finishType = "premium";
    } else if (query.toLowerCase().includes('basic')) {
      baseRate = 40000;
      finishType = "basic";
    } else {
      baseRate = 50000;
    }
  } else if (query.toLowerCase().includes('commercial')) {
    projectType = "commercial construction";
    if (query.toLowerCase().includes('premium')) {
      baseRate = 75000;
      finishType = "premium";
    } else if (query.toLowerCase().includes('basic')) {
      baseRate = 50000;
      finishType = "basic";
    } else {
      baseRate = 60000;
    }
  } else if (query.toLowerCase().includes('renovation') || query.toLowerCase().includes('remodel')) {
    projectType = "renovation";
    if (query.toLowerCase().includes('premium')) {
      baseRate = 40000;
      finishType = "premium";
    } else if (query.toLowerCase().includes('basic')) {
      baseRate = 20000;
      finishType = "basic";
    } else {
      baseRate = 30000;
    }
  } else {
    return null;
  }
  
  const estimate = baseRate * sqm;
  const formattedEstimate = new Intl.NumberFormat('en-PH', { 
    style: 'currency', 
    currency: 'PHP' 
  }).format(estimate);
  
  return {
    content: `Based on your requirements:\n\n• Project Type: ${projectType}\n• Floor Area: ${sqm} sqm\n• Finish Type: ${finishType}\n\nThe estimated cost would be around ${formattedEstimate}.\n\nNote: This is an initial estimate. Actual costs may vary based on:\n• Specific design requirements\n• Material selections\n• Site conditions\n• Timeline requirements\n\nWould you like to schedule a consultation for a detailed quotation?`,
    buttons: [
      { text: "Schedule Consultation", action: "I want to schedule a consultation", path: "/contact" },
      { text: "Modify Estimate", action: "I want to try different specifications" },
      { text: "Learn More", action: "Tell me more about your services", path: "/services" }
    ]
  };
}; 