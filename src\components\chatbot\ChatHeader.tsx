import React from 'react';
import { Minimize, Maximize, X, MessageCircle, Expand, Shrink } from 'lucide-react';

interface ChatHeaderProps {
  isMinimized: boolean;
  isExpanded: boolean;
  toggleChat: () => void;
  toggleExpand: () => void;
  closeChat: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ isMinimized, isExpanded, toggleChat, toggleExpand, closeChat }) => {
  return (
    <div className="bg-gradient-to-r from-rcs-blue to-blue-700 text-white p-4 md:p-5 flex items-center justify-between shadow-md relative overflow-hidden h-[65px] md:h-[76px] min-h-[65px] md:min-h-[76px] flex-shrink-0 border-b border-blue-800">
      <div className="absolute top-0 left-0 w-full h-full opacity-15">
        <div className="absolute top-0 left-0 w-20 h-20 bg-white rounded-full -ml-10 -mt-10"></div>
        <div className="absolute bottom-0 right-0 w-16 h-16 bg-white rounded-full -mr-8 -mb-8"></div>
        <div className="absolute top-10 right-20 w-6 h-6 bg-white rounded-full opacity-20"></div>
        <div className="absolute bottom-8 left-24 w-4 h-4 bg-white rounded-full opacity-20"></div>
        <svg className="absolute inset-0 w-full h-full opacity-10" xmlns="http://www.w3.org/2000/svg">
          <pattern id="wave-pattern" x="0" y="0" width="100" height="20" patternUnits="userSpaceOnUse">
            <path d="M0,10 C30,20 70,0 100,10 L100,0 L0,0 Z" fill="white"/>
          </pattern>
          <rect x="0" y="0" width="100%" height="100%" fill="url(#wave-pattern)"/>
        </svg>
      </div>
      <div className="flex items-center relative z-10">
        <div className="bg-white p-2 md:p-2.5 rounded-full mr-2 md:mr-3 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
          <MessageCircle className="h-4 w-4 md:h-5 md:w-5 text-rcs-blue" />
        </div>
        <div>
          <h3 className="font-semibold text-sm md:text-base mb-0.5 md:mb-1">RCS Assistant</h3>
          <div className="flex items-center gap-1.5">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse shadow-sm shadow-green-300"></div>
            <p className="text-[10px] md:text-xs text-gray-100">{isMinimized ? "Online" : "Online | Ready to help"}</p>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-1 md:space-x-2 relative z-10">
        {!isMinimized && (
          <button 
            onClick={toggleExpand}
            className="hidden md:flex hover:bg-white/30 rounded-full p-1.5 transition-all duration-300 hover:scale-110"
            aria-label={isExpanded ? "Collapse chat" : "Expand chat"}
            title={isExpanded ? "Collapse chat" : "Expand chat"}
          >
            {isExpanded ? <Shrink size={16} /> : <Expand size={16} />}
          </button>
        )}
        <button 
          onClick={toggleChat} 
          className="hover:bg-white/30 rounded-full p-1.5 transition-all duration-300 hover:scale-110 active:bg-white/50 focus:bg-white/40 focus:outline-none"
          aria-label={isMinimized ? "Maximize chat" : "Minimize chat"}
          title={isMinimized ? "Maximize chat" : "Minimize chat"}
        >
          {isMinimized ? <Maximize size={16} /> : <Minimize size={16} />}
        </button>
        <button 
          onClick={closeChat} 
          className="hover:bg-white/30 rounded-full p-1.5 transition-all duration-300 hover:scale-110 ml-1 active:bg-white/50 focus:bg-white/40 focus:outline-none"
          aria-label="Close chat"
          title="Close chat"
        >
          <X size={16} className="md:w-4 md:h-4" />
        </button>
      </div>
    </div>
  );
};

export default ChatHeader; 