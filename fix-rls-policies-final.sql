-- =====================================================
-- FINAL FIX: RLS Policies Setup (Fixed JSON format)
-- Run this in Supabase SQL Editor to fix database errors
-- =====================================================

-- 1. DISABLE RLS temporarily to allow admin operations
ALTER TABLE IF EXISTS projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS services DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS company_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS media_files DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS admin_users DISABLE ROW LEVEL SECURITY;

-- 2. DROP EXISTING POLICIES (if they exist)
DROP POLICY IF EXISTS "Allow all operations" ON projects;
DROP POLICY IF EXISTS "Allow all operations" ON services;
DROP POLICY IF EXISTS "Allow all operations" ON company_info;
DROP POLICY IF EXISTS "Allow all operations" ON blog_posts;
DROP POLICY IF EXISTS "Allow all operations" ON media_files;
DROP POLICY IF EXISTS "Allow all operations" ON admin_users;

-- 3. SIMPLE RLS POLICIES FOR TABLES (Allow all operations)
-- Projects
CREATE POLICY "Allow all operations" ON projects FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Services  
CREATE POLICY "Allow all operations" ON services FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE services ENABLE ROW LEVEL SECURITY;

-- Company Info
CREATE POLICY "Allow all operations" ON company_info FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE company_info ENABLE ROW LEVEL SECURITY;

-- Blog Posts
CREATE POLICY "Allow all operations" ON blog_posts FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Media Files
CREATE POLICY "Allow all operations" ON media_files FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Admin Users
CREATE POLICY "Allow all operations" ON admin_users FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 4. GRANT PERMISSIONS (only for tables we own)
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 5. CREATE ADMIN USER (with correct columns)
INSERT INTO admin_users (email, role)
VALUES (
  '<EMAIL>',
  'admin'
)
ON CONFLICT (email) DO NOTHING;

-- 6. INSERT DEFAULT COMPANY INFO (with proper JSON format)
INSERT INTO company_info (field_name, field_value)
VALUES 
  ('company_name', '"Rodelas Construction Services"'::jsonb),
  ('company_description', '"Professional construction services in the Philippines"'::jsonb),
  ('company_email', '"<EMAIL>"'::jsonb),
  ('company_phone', '"+63 ************"'::jsonb),
  ('company_address', '"Philippines"'::jsonb),
  ('company_data', '{"name": "Rodelas Construction Services", "description": "Professional construction services", "email": "<EMAIL>", "phone": "+63 ************", "address": "Philippines"}'::jsonb)
ON CONFLICT (field_name) DO UPDATE SET field_value = EXCLUDED.field_value;

-- 7. INSERT SAMPLE DATA FOR TESTING
-- Sample Projects
INSERT INTO projects (title, description, category, location, completion_date, status, featured_image)
VALUES 
  ('Sample Residential Project', 'A beautiful residential construction project', 'Residential', 'Manila, Philippines', '2024-01-15', 'completed', '/placeholder-project.jpg'),
  ('Commercial Building', 'Modern commercial building construction', 'Commercial', 'Makati, Philippines', '2024-02-20', 'completed', '/placeholder-project.jpg')
ON CONFLICT (title) DO NOTHING;

-- Sample Services
INSERT INTO services (title, description, category, icon, features, benefits)
VALUES 
  ('Residential Construction', 'Complete residential building services', 'residential', 'Home', ARRAY['Custom Design', 'Quality Materials'], ARRAY['Durable Construction', 'Modern Design']),
  ('Commercial Construction', 'Professional commercial building services', 'commercial', 'Building', ARRAY['Project Management', 'Quality Assurance'], ARRAY['On-time Delivery', 'Cost Effective'])
ON CONFLICT (title) DO NOTHING;

-- 8. VERIFY SETUP
SELECT 'Tables with RLS enabled:' as status;
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

SELECT 'RLS policies created:' as status;
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public';

-- 9. TEST QUERIES
SELECT 'Testing projects table:' as status;
SELECT COUNT(*) as project_count FROM projects;

SELECT 'Testing services table:' as status;
SELECT COUNT(*) as service_count FROM services;

SELECT 'Testing company_info table:' as status;
SELECT COUNT(*) as company_info_count FROM company_info;

SELECT 'Testing admin_users table:' as status;
SELECT COUNT(*) as admin_count FROM admin_users;

-- 10. SHOW SAMPLE DATA
SELECT 'Sample projects:' as status;
SELECT id, title, category, status FROM projects LIMIT 3;

SELECT 'Sample services:' as status;
SELECT id, title, category FROM services LIMIT 3;

SELECT 'Company info (JSON format):' as status;
SELECT field_name, field_value FROM company_info LIMIT 6;

-- 11. SUCCESS MESSAGE
SELECT 'Database setup completed successfully! You can now use the admin panel.' as final_status;
