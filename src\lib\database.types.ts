export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string
          title: string
          category: string
          location: string | null
          completion_date: string | null
          description: string | null
          featured_image: string | null
          gallery_images: string | null
          client: string | null
          status: string
          is_featured: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          category?: string
          location?: string | null
          completion_date?: string | null
          description?: string | null
          featured_image?: string | null
          gallery_images?: string | null
          client?: string | null
          status?: string
          is_featured?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          category?: string
          location?: string | null
          completion_date?: string | null
          description?: string | null
          featured_image?: string | null
          gallery_images?: string | null
          client?: string | null
          status?: string
          is_featured?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      services: {
        Row: {
          id: string
          title: string
          description: string
          icon: string
          features: Json
          benefits: Json | null
          category: string
          pricing_info: Json | null
          created_at: string
          updated_at: string
          published: boolean
        }
        Insert: {
          id?: string
          title: string
          description: string
          icon: string
          features: Json
          benefits?: Json | null
          category: string
          pricing_info?: Json | null
          created_at?: string
          updated_at?: string
          published?: boolean
        }
        Update: {
          id?: string
          title?: string
          description?: string
          icon?: string
          features?: Json
          benefits?: Json | null
          category?: string
          pricing_info?: Json | null
          created_at?: string
          updated_at?: string
          published?: boolean
        }
      }
      company_info: {
        Row: {
          id: string
          field_name: string
          field_value: string
          field_type: string
          updated_at: string
        }
        Insert: {
          id?: string
          field_name: string
          field_value: string
          field_type: string
          updated_at?: string
        }
        Update: {
          id?: string
          field_name?: string
          field_value?: string
          field_type?: string
          updated_at?: string
        }
      }
      blog_posts: {
        Row: {
          id: string
          title: string
          slug: string
          content: string | null
          excerpt: string | null
          featured_image: string | null
          author: string
          status: string
          published_at: string | null
          created_at: string
          updated_at: string
          published: boolean
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content?: string | null
          excerpt?: string | null
          featured_image?: string | null
          author?: string
          status?: string
          published_at?: string | null
          created_at?: string
          updated_at?: string
          published?: boolean
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string | null
          excerpt?: string | null
          featured_image?: string | null
          author?: string
          status?: string
          published_at?: string | null
          created_at?: string
          updated_at?: string
          published?: boolean
        }
      }
      admin_users: {
        Row: {
          id: string
          email: string
          role: string
          created_at: string
          last_login: string | null
        }
        Insert: {
          id?: string
          email: string
          role?: string
          created_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          email?: string
          role?: string
          created_at?: string
          last_login?: string | null
        }
      }
      media_files: {
        Row: {
          id: string
          filename: string
          original_name: string
          file_path: string
          file_size: number
          mime_type: string
          alt_text: string | null
          created_at: string
        }
        Insert: {
          id?: string
          filename: string
          original_name: string
          file_path: string
          file_size: number
          mime_type: string
          alt_text?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          filename?: string
          original_name?: string
          file_path?: string
          file_size?: number
          mime_type?: string
          alt_text?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types for easier usage
export type Project = Database['public']['Tables']['projects']['Row']
export type ProjectInsert = Database['public']['Tables']['projects']['Insert']
export type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export type Service = Database['public']['Tables']['services']['Row']
export type ServiceInsert = Database['public']['Tables']['services']['Insert']
export type ServiceUpdate = Database['public']['Tables']['services']['Update']

export type CompanyInfo = Database['public']['Tables']['company_info']['Row']
export type CompanyInfoInsert = Database['public']['Tables']['company_info']['Insert']
export type CompanyInfoUpdate = Database['public']['Tables']['company_info']['Update']

export type BlogPost = Database['public']['Tables']['blog_posts']['Row']
export type BlogPostInsert = Database['public']['Tables']['blog_posts']['Insert']
export type BlogPostUpdate = Database['public']['Tables']['blog_posts']['Update']

export type AdminUser = Database['public']['Tables']['admin_users']['Row']
export type AdminUserInsert = Database['public']['Tables']['admin_users']['Insert']
export type AdminUserUpdate = Database['public']['Tables']['admin_users']['Update']

export type MediaFile = Database['public']['Tables']['media_files']['Row']
export type MediaFileInsert = Database['public']['Tables']['media_files']['Insert']
export type MediaFileUpdate = Database['public']['Tables']['media_files']['Update']
