-- =====================================================
-- COMPREHENSIVE FIX FOR ALL DATABASE AND STORAGE ISSUES
-- Run this in your Supabase SQL Editor to fix everything
-- =====================================================

-- 1. ADD PUBLISHED COLUMN TO EXISTING TABLES
-- =====================================================

-- Add published column to projects table
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT true;

-- Add published column to services table  
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT true;

-- Add published column to blog_posts table (if it exists)
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT false;

-- Update existing projects to be published by default
UPDATE projects SET published = true WHERE published IS NULL;

-- Update existing services to be published by default  
UPDATE services SET published = true WHERE published IS NULL;

-- 2. FIX STORAGE BUCKET AND RLS POLICIES
-- =====================================================

-- Create storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('rcs-media', 'rcs-media', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can upload media files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view media files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete media files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update media files" ON storage.objects;

-- Create permissive storage policies for rcs-media bucket
CREATE POLICY "Allow public uploads to rcs-media"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'rcs-media');

CREATE POLICY "Allow public access to rcs-media"
ON storage.objects FOR SELECT
USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public updates to rcs-media"
ON storage.objects FOR UPDATE
USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public deletes from rcs-media"
ON storage.objects FOR DELETE
USING (bucket_id = 'rcs-media');

-- 3. CREATE MEDIA_FILES TABLE IF NOT EXISTS
-- =====================================================

CREATE TABLE IF NOT EXISTS media_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    alt_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. VERIFY EVERYTHING IS WORKING
-- =====================================================

-- Check projects table structure
SELECT 'Projects table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position;

-- Check services table structure
SELECT 'Services table columns:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'services' 
ORDER BY ordinal_position;

-- Check storage bucket
SELECT 'Storage bucket info:' as info;
SELECT id, name, public FROM storage.buckets WHERE id = 'rcs-media';

-- Check storage policies
SELECT 'Storage policies:' as info;
SELECT policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects';

-- Test data - show existing projects
SELECT 'Existing projects:' as info;
SELECT id, title, category, published, created_at 
FROM projects 
ORDER BY created_at DESC 
LIMIT 5;

SELECT 'Setup completed successfully!' as status;
