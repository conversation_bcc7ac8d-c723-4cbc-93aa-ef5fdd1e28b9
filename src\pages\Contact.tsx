import React, { useEffect, useRef } from 'react';
import { Phone, Mail, MapPin, Send } from 'lucide-react';
import { useContactForm } from "@/hooks/use-contact-form";
import { useSearchParams } from 'react-router-dom';
import { getCsrfToken } from '@/lib/csrf-protection';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import <PERSON><PERSON>aptcha from '@/components/ReCaptcha';

const Contact = () => {
  const {
    formData,
    isSubmitting,
    handleChange,
    handleSubmit,
    resetForm,
    setRecaptchaToken,
    toast
  } = useContactForm();
  
  const [searchParams] = useSearchParams();
  const formRef = useRef<HTMLFormElement>(null);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const hasHandledParams = useRef(false);

  // Fix for mobile keyboard scrolling issue
  useEffect(() => {
    if (!formRef.current) return;

    // Add touch-action manipulation to form elements
    formRef.current.style.touchAction = 'manipulation';
    
    // Apply to all input and textarea elements in the form
    const inputs = formRef.current.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      (input as HTMLElement).style.touchAction = 'manipulation';
    });

    // Handler for when an input gets focus - prevent unwanted scrolling
    const handleFocusIn = (e: FocusEvent) => {
      // Prevent any automatic scrolling
      e.preventDefault();
      
      // Ensure the element stays visible without scrolling to top
      const element = e.target as HTMLElement;
      const rect = element.getBoundingClientRect();
      
      // Only scroll if the element is not fully visible in the viewport
      if (rect.top < 0 || rect.bottom > window.innerHeight) {
        // Scroll just enough to make element visible
        element.scrollIntoView({ block: 'center', behavior: 'smooth' });
      }
    };

    // Apply the focus handler to all inputs
    inputs.forEach(input => {
      input.addEventListener('focusin', handleFocusIn as EventListener);
    });

    return () => {
      if (!formRef.current) return;
      const inputs = formRef.current.querySelectorAll('input, textarea');
      inputs.forEach(input => {
        input.removeEventListener('focusin', handleFocusIn as EventListener);
      });
    };
  }, []);

  useEffect(() => {
    // Only handle URL parameters once on initial load
    if (!hasHandledParams.current) {
      // Only scroll to top on first page navigation, not during form interactions
      const isInitialLoad = document.referrer.includes(window.location.host);
      if (!isInitialLoad) {
        window.scrollTo(0, 0);
      }
      
      // Check for URL parameters to pre-fill the form
      const name = searchParams.get('name');
      const email = searchParams.get('email');
      const phone = searchParams.get('phone');
      const message = searchParams.get('message');
      const source = searchParams.get('source');
      
      // If we have parameters, pre-fill the form with event that simulates user input
      if (name || email || phone || message) {
        const createChangeEvent = (name: string, value: string) => ({
          target: { name, value }
        } as React.ChangeEvent<HTMLInputElement>);
        
        if (name) handleChange(createChangeEvent('from_name', name));
        if (email) handleChange(createChangeEvent('from_email', email));
        if (phone) handleChange(createChangeEvent('phone', phone));
        
        // Prepare message with source info
        let fullMessage = message || '';
        if (source === 'chatbot') {
          fullMessage = `[Sent via Chatbot]\n\n${fullMessage}`;
        }
        if (fullMessage) handleChange(createChangeEvent('message', fullMessage));
        
        // Scroll to form only on initial URL parameter load
        setTimeout(() => {
          document.getElementById('form')?.scrollIntoView({ behavior: 'smooth' });
        }, 500);
      }
      
      hasHandledParams.current = true;
    }
  }, [searchParams, handleChange]);

  const handleRecaptchaChange = (token: string | null) => {
    if (token) {
      setRecaptchaToken(token);
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // Always prevent default form submission

    if (!formData.recaptchaToken) {
      // Show error message when reCAPTCHA is not completed
      toast({
        title: 'Verification Required',
        description: 'Please check the reCAPTCHA box to verify you are not a robot.',
        variant: 'destructive'
      });
      
      // Focus on recaptcha
      if (recaptchaRef.current) {
        // Try to make the reCAPTCHA area visible to the user
        const element = document.querySelector('.recaptcha-container');
        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return;
    }
    
    handleSubmit(e);
  };

  return (
    <>
      <div className="pt-24 pb-20 bg-rcs-blue/90">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">Contact Us</h1>
            <p className="text-xl text-white/80">
              Reach out to discuss your construction needs or request a quote. We're here to help bring your vision to life.
            </p>
          </div>
        </div>
      </div>

      <div className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-2xl font-bold text-rcs-blue mb-6">Get in Touch</h2>
              <p className="text-gray-600 mb-8">
                Whether you have a question about our services, need a quote, or want to discuss your project, our team is ready to assist you.
              </p>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-rcs-blue rounded-full p-3 mr-4">
                    <Phone className="text-white" size={20} />
                  </div>
                  <div>
                    <h3 className="font-bold text-rcs-blue">Phone</h3>
                    <p className="text-gray-600 mt-1">Globe: 09670598903/09951858305</p>
                    <p className="text-gray-600 mt-1">Landline: ************</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-rcs-blue rounded-full p-3 mr-4">
                    <Mail className="text-white" size={20} />
                  </div>
                  <div>
                    <h3 className="font-bold text-rcs-blue">Email</h3>
                    <p className="text-gray-600 mt-1"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-rcs-blue rounded-full p-3 mr-4">
                    <MapPin className="text-white" size={20} />
                  </div>
                  <div>
                    <h3 className="font-bold text-rcs-blue">Office Location</h3>
                    <p className="text-gray-600 mt-1">
                      Block 8 Lot 7 Phase 2 Gregory Street,<br />
                      St. Joseph Village, 7 Marinig, Cabuyao, 4025 Laguna
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-12">
                <h3 className="text-xl font-bold text-rcs-blue mb-4">Office Hours</h3>
                <div className="space-y-2 text-gray-600">
                  <div className="flex justify-between">
                    <span>Monday - Friday:</span>
                    <span>8:00 AM - 6:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Saturday:</span>
                    <span>9:00 AM - 2:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sunday:</span>
                    <span>Closed</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-bold text-rcs-blue mb-6">Send Us a Message</h2>
              
              <form id="form" ref={formRef} onSubmit={handleFormSubmit} className="space-y-6">
                {/* Hidden CSRF token field */}
                <input
                  type="hidden"
                  name="csrfToken"
                  value={formData.csrfToken}
                />
                
                <div>
                  <label htmlFor="from_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="from_name"
                    name="from_name"
                    value={formData.from_name}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rcs-blue"
                    placeholder="Full Name"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="from_email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="from_email"
                    name="from_email"
                    value={formData.from_email}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rcs-blue"
                    placeholder="Email Address"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rcs-blue"
                    placeholder="Phone Number"
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={5}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rcs-blue"
                    placeholder="Tell us about your project or question..."
                    required
                  ></textarea>
                </div>
                
                {/* ReCAPTCHA v2 Checkbox */}
                <ReCaptcha
                  ref={recaptchaRef}
                  onChange={handleRecaptchaChange}
                  className="flex justify-center"
                />
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-rcs-blue text-white font-semibold py-3 rounded-md hover:bg-blue-900 transition-colors duration-300 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send size={18} className="mr-2" />
                      Send Message
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <div id="location" className="py-16 bg-gray-100">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-2xl font-bold text-rcs-blue mb-8 text-center">Our Location</h2>
          <div className="h-96 rounded-lg overflow-hidden shadow-md">
            {/* Primary Google Maps iframe with fallback */}
            <iframe
              title="map"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d5070.085611400343!2d121.1527285760898!3d14.267298285147966!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3397d9c7daf0caf5%3A0xe4fafc59e87b1190!2sThe%20Brew%20Deck!5e1!3m2!1sen!2sph!4v1744209848744!5m2!1sen!2sph"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              onError={(e) => {
                // If iframe fails to load, replace with a link to Google Maps
                const target = e.target as HTMLIFrameElement;
                const container = target.parentElement;
                if (container) {
                  const link = document.createElement('a');
                  link.href = "https://www.google.com/maps?q=14.267298285147966,121.1527285760898"; // Direct link to coordinates
                  link.target = "_blank";
                  link.rel = "noopener noreferrer";
                  link.className = "block w-full h-full relative";
                  
                  // Use a static map image as background
                  const img = document.createElement('img');
                  img.src = "https://maps.googleapis.com/maps/api/staticmap?center=14.267298285147966,121.1527285760898&zoom=15&size=800x600&maptype=roadmap&markers=color:red%7C14.267298285147966,121.1527285760898&key="; // No API key needed for static image
                  img.alt = "Map to Rodelas Construction Services";
                  img.className = "w-full h-full object-cover";
                  
                  // Add a button on top of the image
                  const button = document.createElement('div');
                  button.className = "absolute inset-0 flex items-center justify-center";
                  button.innerHTML = '<div class="bg-rcs-blue text-white px-4 py-2 rounded-md shadow-md hover:bg-blue-900 transition-colors">View on Google Maps</div>';
                  
                  link.appendChild(img);
                  link.appendChild(button);
                  container.innerHTML = '';
                  container.appendChild(link);
                }
              }}
            ></iframe>
          </div>
          
          {/* Add text directions below the map for better accessibility */}
          <div className="mt-6 text-center text-gray-700">
            <p>Block 8 Lot 7 Phase 2 Gregory Street, St. Joseph Village, 7 Marinig, Cabuyao, 4025 Laguna</p>
            <p className="mt-2">
              <a 
                href="https://www.google.com/maps?q=14.267298285147966,121.1527285760898" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-rcs-blue hover:underline inline-flex items-center"
              >
                <MapPin size={16} className="mr-1" /> Get Directions
              </a>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Contact;
