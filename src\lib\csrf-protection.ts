/**
 * CSRF Protection Utility
 * 
 * This module provides functions to handle CSRF protection for forms.
 * It generates, stores, and validates CSRF tokens to prevent Cross-Site Request Forgery attacks.
 */

// Generate a random token string
const generateRandomToken = (length: number = 32): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  
  // Use crypto.getRandomValues for secure random generation when available
  if (window.crypto && window.crypto.getRandomValues) {
    const values = new Uint32Array(length);
    window.crypto.getRandomValues(values);
    for (let i = 0; i < length; i++) {
      token += characters.charAt(values[i] % characters.length);
    }
  } else {
    // Fallback to Math.random (less secure but still viable)
    for (let i = 0; i < length; i++) {
      token += characters.charAt(Math.floor(Math.random() * characters.length));
    }
  }
  
  return token;
};

// Storage key in sessionStorage
const CSRF_TOKEN_KEY = 'rcs_csrf_token';

/**
 * Generate a new CSRF token and store it in sessionStorage
 * @returns {string} The generated CSRF token
 */
export const generateCsrfToken = (): string => {
  const token = generateRandomToken();
  
  try {
    // Store in sessionStorage (cleared when tab/window is closed)
    sessionStorage.setItem(CSRF_TOKEN_KEY, token);
    return token;
  } catch (error) {
    console.error('Failed to store CSRF token in sessionStorage', error);
    return token; // Still return the token even if storage fails
  }
};

/**
 * Get the stored CSRF token, or generate a new one if none exists
 * @returns {string} The CSRF token
 */
export const getCsrfToken = (): string => {
  try {
    // Try to get existing token
    const token = sessionStorage.getItem(CSRF_TOKEN_KEY);
    
    // If no token exists, generate a new one
    if (!token) {
      return generateCsrfToken();
    }
    
    return token;
  } catch (error) {
    // If sessionStorage is not available, generate a new token
    console.error('Failed to retrieve CSRF token from sessionStorage', error);
    return generateRandomToken();
  }
};

/**
 * Validate a CSRF token against the stored token
 * @param {string} token - The token to validate
 * @returns {boolean} True if the token is valid, false otherwise
 */
export const validateCsrfToken = (token: string): boolean => {
  if (!token) return false;
  
  try {
    const storedToken = sessionStorage.getItem(CSRF_TOKEN_KEY);
    
    // No stored token means we can't validate
    if (!storedToken) return false;
    
    // Compare tokens using constant-time comparison to prevent timing attacks
    return timingSafeEqual(token, storedToken);
  } catch (error) {
    console.error('Failed to validate CSRF token', error);
    return false;
  }
};

/**
 * Constant-time string comparison to prevent timing attacks
 * @param {string} a - First string
 * @param {string} b - Second string
 * @returns {boolean} True if strings are equal
 */
const timingSafeEqual = (a: string, b: string): boolean => {
  if (a.length !== b.length) return false;
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
};

/**
 * Refresh the CSRF token
 * This is useful after form submission to prevent token reuse
 */
export const refreshCsrfToken = (): void => {
  generateCsrfToken();
}; 