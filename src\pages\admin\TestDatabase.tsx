import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/database';

const TestDatabase = () => {
  const [title, setTitle] = useState('');
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<any[]>([]);
  const { toast } = useToast();

  const testCreateProject = async () => {
    if (!title) {
      toast({
        title: 'Error',
        description: 'Please enter a title',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      console.log('Testing project creation...');
      
      const projectData = {
        title: title,
        description: 'Test project from test page',
        category: 'Residential',
        location: 'Test Location',
        completion_date: '2024-01-15',
        status: 'completed',
        featured_image: 'https://via.placeholder.com/800x600',
        gallery_images: '',
        client: 'Test Client',
        is_featured: false
      };

      console.log('Project data:', projectData);

      const result = await db.projects.create(projectData);
      console.log('Create result:', result);

      toast({
        title: 'Success',
        description: 'Project created successfully!'
      });

      setTitle('');
      fetchProjects();

    } catch (error) {
      console.error('Error creating project:', error);
      toast({
        title: 'Error',
        description: `Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      console.log('Fetching projects...');
      const projectsData = await db.projects.getAll();
      console.log('Projects fetched:', projectsData);
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch projects',
        variant: 'destructive'
      });
    }
  };

  const testConnection = async () => {
    try {
      console.log('Testing database connection...');
      await fetchProjects();
      toast({
        title: 'Success',
        description: 'Database connection working!'
      });
    } catch (error) {
      console.error('Connection test failed:', error);
      toast({
        title: 'Error',
        description: 'Database connection failed',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold">Database Test Page</h1>
        <p className="text-gray-600">Test database operations directly</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Connection</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={testConnection} className="w-full">
              Test Database Connection
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Create Test Project</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter project title"
              />
            </div>
            <Button 
              onClick={testCreateProject} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Creating...' : 'Create Test Project'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Existing Projects ({projects.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {projects.length === 0 ? (
            <p className="text-gray-500">No projects found</p>
          ) : (
            <div className="space-y-2">
              {projects.map((project) => (
                <div key={project.id} className="p-3 border rounded">
                  <h3 className="font-medium">{project.title}</h3>
                  <p className="text-sm text-gray-600">
                    {project.category} • {project.location} • {project.status}
                  </p>
                  <p className="text-xs text-gray-400">
                    Created: {new Date(project.created_at).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          )}
          <Button onClick={fetchProjects} variant="outline" className="mt-4">
            Refresh Projects
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestDatabase;
