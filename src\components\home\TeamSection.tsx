import React, { useState, useEffect } from 'react';
import { Linkedin, Mail } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { AnimatedTestimonials } from '@/components/ui/animated-testimonials';
import { getOptimizedImagePath } from '@/lib/imageUtils';

interface TeamMember {
  id: number;
  name: string;
  role: string;
  bio: string;
  image: string;
  social: {
    linkedin?: string;
    email: string;
  };
}

const teamMembers: TeamMember[] = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Founder & CEO",
    bio: "With over 20 years of construction industry experience, <PERSON> leads RCS with a vision for excellence and innovation in every project.",
    image: "/rodelas.webp",
    social: {
      linkedin: "https://linkedin.com",
      email: "<EMAIL>"
    }
  },
  {
    id: 2,
    name: "<PERSON> Rodelas",
    role: "Project Manager",
    bio: "Ace ensures every project is delivered on time and within budget while maintaining the highest quality standards our clients expect.",
    image: "/ace.webp",
    social: {
      linkedin: "https://linkedin.com",
      email: "<EMAIL>"
    }
  },
];

// Convert team members to testimonial format for the animated component
const testimonialFormat = teamMembers.map(member => ({
  quote: member.bio,
  name: member.name,
  designation: member.role,
  src: member.image
}));

const TeamSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = React.useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-rcs-gray relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-rcs-blue mb-4">Meet Our Team</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our team of experienced professionals is dedicated to bringing your construction vision to life.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <AnimatedTestimonials testimonials={testimonialFormat} autoplay={isVisible} />
        </div>
      </div>
    </section>
  );
};

export default TeamSection;
