-- =====================================================
-- FIX STORAGE POLICIES for rcs-media bucket
-- Run this in Supabase SQL Editor
-- =====================================================

-- 1. Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 2. Drop existing policies (if any)
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Public Upload" ON storage.objects;
DROP POLICY IF EXISTS "Public Update" ON storage.objects;
DROP POLICY IF EXISTS "Public Delete" ON storage.objects;
DROP POLICY IF EXISTS "Allow public uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow all" ON storage.objects;

-- 3. Create comprehensive storage policies
CREATE POLICY "Allow public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public insert" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'rcs-media');

CREATE POLICY "Allow public update" ON storage.objects
FOR UPDATE USING (bucket_id = 'rcs-media');

CREATE POLICY "Allow public delete" ON storage.objects
FOR DELETE USING (bucket_id = 'rcs-media');

-- 4. Alternative: Single policy for all operations
CREATE POLICY "Allow all operations on rcs-media" ON storage.objects
FOR ALL USING (bucket_id = 'rcs-media') WITH CHECK (bucket_id = 'rcs-media');

-- 5. Grant permissions to storage
GRANT ALL ON storage.objects TO anon, authenticated;
GRANT ALL ON storage.buckets TO anon, authenticated;

-- 6. Verify bucket exists and is public
SELECT 'Checking bucket:' as status;
SELECT id, name, public FROM storage.buckets WHERE id = 'rcs-media';

-- 7. Check storage policies
SELECT 'Storage policies:' as status;
SELECT policyname, cmd, qual FROM pg_policies WHERE tablename = 'objects';

-- 8. Success message
SELECT 'Storage policies updated! Try uploading again.' as final_status;
