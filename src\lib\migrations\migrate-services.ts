import { db } from '../database';

// Service data from Services.tsx
const servicesData = [
  {
    id: "commercial-construction",
    title: "Commercial Construction",
    description: "Full-service commercial construction for retail spaces, offices, and industrial facilities.",
    icon: "Building",
    features: [
      "Custom office buildings and retail spaces",
      "Industrial and manufacturing facilities",
      "Healthcare and institutional buildings",
      "Restaurant and hospitality construction"
    ],
    benefits: [
      "Increased property value",
      "Enhanced business operations and efficiency",
      "Modern, energy-efficient facilities",
      "Compliance with all commercial building codes"
    ],
    category: "commercial",
    is_featured: true,
    sort_order: 1
  },
  {
    id: "residential-construction",
    title: "Residential Construction",
    description: "Quality home building services, from custom homes to multi-family residential projects.",
    icon: "Home",
    features: [
      "Custom home design and construction",
      "Multi-family residential projects",
      "Residential community development",
      "Luxury home construction"
    ],
    benefits: [
      "Personalized living spaces tailored to your needs",
      "Energy-efficient designs that reduce utility costs",
      "Quality craftsmanship that stands the test of time",
      "Modern amenities that enhance comfort and convenience"
    ],
    category: "residential",
    is_featured: true,
    sort_order: 2
  },
  {
    id: "renovation-remodeling",
    title: "Renovation & Remodeling",
    description: "Transform your existing spaces with our expert renovation and remodeling services.",
    icon: "PenTool",
    features: [
      "Kitchen and bathroom renovations",
      "Office and commercial space remodeling",
      "Home additions and expansions",
      "Historic building restoration"
    ],
    benefits: [
      "Breathe new life into outdated spaces",
      "Increase property value and marketability",
      "Improve functionality and flow of spaces",
      "Incorporate modern amenities while preserving character"
    ],
    category: "specialized",
    is_featured: true,
    sort_order: 3
  },
  {
    id: "construction-management",
    title: "Construction Management",
    description: "Professional management of your construction project from start to finish.",
    icon: "BarChart3",
    features: [
      "Project planning and scheduling",
      "Budget development and management",
      "Quality control and assurance",
      "Risk assessment and mitigation"
    ],
    benefits: [
      "Reduced project delays and cost overruns",
      "Single point of accountability",
      "Improved communication between all parties",
      "Expert handling of complex project requirements"
    ],
    category: "management",
    is_featured: true,
    sort_order: 4
  },
  {
    id: "house-construction",
    title: "House Construction",
    description: "Complete construction services for residential properties, including 2-storey houses in premium locations like South Forbes, Silang Cavite. We handle everything from foundation to finishing touches.",
    icon: "Building",
    features: [
      "Custom house design and planning",
      "Quality materials and workmanship",
      "Strict adherence to building codes",
      "Timely project completion",
      "Regular progress updates"
    ],
    benefits: [
      "Expert project management",
      "Skilled workforce",
      "Modern construction techniques",
      "Comprehensive warranty",
      "After-construction support"
    ],
    category: "residential",
    is_featured: false,
    sort_order: 5
  },
  {
    id: "carpentry",
    title: "Carpentry",
    description: "Professional carpentry services for both structural and decorative elements, ensuring precision and durability in every project.",
    icon: "Wrench",
    features: [
      "Custom furniture and cabinetry",
      "Wooden flooring installation",
      "Door and window framing",
      "Staircase construction",
      "Wooden decking and pergolas"
    ],
    benefits: [
      "High-quality materials",
      "Precision craftsmanship",
      "Custom designs",
      "Durable finishes",
      "Maintenance guidance"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 6
  },
  {
    id: "masonry",
    title: "Masonry",
    description: "Expert masonry work for foundations, walls, and other structural elements using high-quality materials and proven techniques.",
    icon: "HardHat",
    features: [
      "Concrete work and foundations",
      "Brick and block laying",
      "Stone masonry",
      "Retaining walls",
      "Concrete finishing"
    ],
    benefits: [
      "Structural integrity",
      "Weather resistance",
      "Long-lasting durability",
      "Professional finishing",
      "Code compliance"
    ],
    category: "commercial",
    is_featured: false,
    sort_order: 7
  },
  {
    id: "steel-works",
    title: "Steel Works",
    description: "Comprehensive steel fabrication and installation services for structural and architectural applications.",
    icon: "Ruler",
    features: [
      "Structural steel framing",
      "Metal roofing",
      "Steel reinforcement",
      "Metal fabrication",
      "Steel finishing"
    ],
    benefits: [
      "High strength-to-weight ratio",
      "Durability and longevity",
      "Fire resistance",
      "Design flexibility",
      "Low maintenance"
    ],
    category: "commercial",
    is_featured: false,
    sort_order: 8
  },
  {
    id: "renovation",
    title: "Renovation",
    description: "Transform your existing space with our professional renovation services, from minor updates to complete makeovers.",
    icon: "Home",
    features: [
      "Interior remodeling",
      "Exterior upgrades",
      "Space optimization",
      "Modernization",
      "Structural improvements"
    ],
    benefits: [
      "Enhanced functionality",
      "Improved aesthetics",
      "Increased property value",
      "Energy efficiency",
      "Custom solutions"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 9
  },
  {
    id: "painting",
    title: "Painting",
    description: "Professional painting services for both interior and exterior surfaces, using high-quality paints and techniques.",
    icon: "PenTool",
    features: [
      "Interior painting",
      "Exterior painting",
      "Surface preparation",
      "Color consultation",
      "Special finishes"
    ],
    benefits: [
      "Enhanced aesthetics",
      "Protection from elements",
      "Long-lasting finish",
      "Professional application",
      "Clean work environment"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 10
  },
  {
    id: "building-ventilation",
    title: "Building Ventilation",
    description: "Expert installation and maintenance of ventilation systems to ensure optimal air quality and comfort.",
    icon: "BarChart3",
    features: [
      "Ventilation system design",
      "Installation and maintenance",
      "Air quality solutions",
      "Energy-efficient systems",
      "Regular maintenance"
    ],
    benefits: [
      "Improved air quality",
      "Energy efficiency",
      "Reduced humidity",
      "Better comfort",
      "Health benefits"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 11
  },
  {
    id: "plumbing",
    title: "Plumbing",
    description: "Comprehensive plumbing services for residential and commercial properties, ensuring reliable water supply and drainage systems.",
    icon: "Wrench",
    features: [
      "Pipe installation and repair",
      "Fixture installation",
      "Water heater services",
      "Drain cleaning",
      "Emergency repairs"
    ],
    benefits: [
      "Reliable water supply",
      "Efficient drainage",
      "Water conservation",
      "Professional installation",
      "24/7 emergency service"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 12
  },
  {
    id: "demolition",
    title: "Demolition",
    description: "Safe and efficient demolition services for structures of all sizes, with proper waste management and site preparation.",
    icon: "HardHat",
    features: [
      "Structural demolition",
      "Interior demolition",
      "Debris removal",
      "Site preparation",
      "Safety compliance"
    ],
    benefits: [
      "Safe execution",
      "Efficient process",
      "Proper waste disposal",
      "Site preparation",
      "Minimal disruption"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 13
  },
  {
    id: "electrical-works",
    title: "Electrical Works",
    description: "Professional electrical installation and maintenance services, ensuring safe and efficient power distribution.",
    icon: "BarChart3",
    features: [
      "Wiring installation",
      "Panel upgrades",
      "Lighting installation",
      "Safety inspections",
      "Emergency repairs"
    ],
    benefits: [
      "Safe installation",
      "Energy efficiency",
      "Code compliance",
      "Reliable power supply",
      "Professional maintenance"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 14
  },
  {
    id: "air-conditioning",
    title: "Air-Conditioning",
    description: "Expert installation and maintenance of air conditioning systems for optimal comfort and energy efficiency.",
    icon: "BarChart3",
    features: [
      "AC installation",
      "System maintenance",
      "Repair services",
      "Energy-efficient solutions",
      "Regular check-ups"
    ],
    benefits: [
      "Optimal comfort",
      "Energy savings",
      "Improved air quality",
      "Professional service",
      "Extended system life"
    ],
    category: "specialized",
    is_featured: false,
    sort_order: 15
  }
];

export async function migrateServices() {
  console.log('Starting services migration...');
  
  try {
    for (const service of servicesData) {
      console.log(`Migrating service: ${service.title}`);
      
      // Insert service
      const insertedService = await db.services.create({
        id: service.id,
        title: service.title,
        description: service.description,
        icon: service.icon,
        features: service.features,
        benefits: service.benefits,
        category: service.category,
        is_featured: service.is_featured,
        sort_order: service.sort_order
      });
      
      console.log(`✅ Successfully migrated: ${service.title}`);
    }
    
    console.log('✅ All services migrated successfully!');
    return { success: true, message: 'Services migrated successfully' };
  } catch (error) {
    console.error('❌ Error migrating services:', error);
    return { success: false, error };
  }
}
