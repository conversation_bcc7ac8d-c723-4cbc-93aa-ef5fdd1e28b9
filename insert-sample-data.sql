-- =====================================================
-- INSERT SAMPLE DATA DIRECTLY
-- Run this to populate your database with sample projects and services
-- =====================================================

-- 1. INSERT SAMPLE PROJECTS
INSERT INTO projects (
    title, description, category, location, completion_date, 
    status, featured_image, gallery_images, client, is_featured, 
    published, features, highlights, tags
) VALUES 
(
    'ARC & SONS Barbershop',
    'Rodelas Construction Services proudly completed the full interior fit-out of ARC & SONS Barbershop, located in the stylish commercial zone of South Forbes, Silang, Cavite.',
    'Commercial',
    'South Forbes, Silang, Cavite',
    '2024-01-01',
    'completed',
    '/images/projects/commercial/arcandsons/main.webp',
    '[]'::jsonb,
    'ARC & SONS Barbershop',
    true,
    true,
    '[{"title": "Cabinet Fabrication", "description": "Custom cabinet and counter fabrication"}, {"title": "Interior Carpentry", "description": "Interior carpentry and woodwork with premium finishes"}]'::jsonb,
    '["Modern and welcoming grooming environment", "Clean, bold and professional brand identity"]'::jsonb,
    '["Commercial", "Interior", "Barbershop"]'::jsonb
),
(
    'The Racha Project',
    'At Rodelas Construction Services, we turn your vision into reality. The Racha Project is one of our most refined residential builds — combining modern elegance with functionality.',
    'Residential',
    'Silang, Cavite',
    '2024-01-01',
    'completed',
    '/images/projects/residential/racha_project/1.webp',
    '[]'::jsonb,
    'Racha Family',
    true,
    true,
    '[{"title": "Modern Design", "description": "Contemporary architectural design"}, {"title": "Quality Materials", "description": "Premium construction materials"}]'::jsonb,
    '["Modern elegance with functionality", "Thoughtfully designed and constructed"]'::jsonb,
    '["Residential", "Modern", "Custom Home"]'::jsonb
),
(
    'Cassasis Residential Building',
    'From Blueprints to Reality: Our Cassasis Residential Building at Chateux de Paris, Silang, Cavite is finished. Our sincere gratitude for Cassasis Family for trusting the ability and craftmanship of Rodelas Construction Services.',
    'Residential',
    'Chateux de Paris, Silang, Cavite',
    '2024-01-01',
    'completed',
    '/images/projects/residential/cassasis_residential/main.webp',
    '[]'::jsonb,
    'Cassasis Family',
    true,
    true,
    '[{"title": "Custom Design", "description": "Tailored residential design"}, {"title": "Quality Craftsmanship", "description": "Expert construction techniques"}]'::jsonb,
    '["Form meets function in a masterpiece built to endure", "Trusted craftsmanship"]'::jsonb,
    '["Residential", "Custom Build", "Family Home"]'::jsonb
),
(
    'Ayala Alabang 2-Storey Residential Build',
    'A stunning 2-storey residential project in the prestigious Ayala Alabang area, showcasing modern architecture and premium finishes.',
    'Residential',
    'Ayala Alabang, Muntinlupa',
    '2024-01-01',
    'completed',
    'https://via.placeholder.com/800x600/0066cc/ffffff?text=Ayala+Project',
    '[]'::jsonb,
    'Private Client',
    false,
    true,
    '[{"title": "2-Storey Design", "description": "Multi-level residential construction"}, {"title": "Premium Location", "description": "Prestigious Ayala Alabang area"}]'::jsonb,
    '["Modern architecture", "Premium finishes", "Prestigious location"]'::jsonb,
    '["Residential", "2-Storey", "Premium"]'::jsonb
),
(
    'South Forbes Villas',
    'Luxury villa construction in South Forbes, featuring contemporary design and high-end finishes for discerning homeowners.',
    'Residential',
    'South Forbes, Silang, Cavite',
    '2024-01-01',
    'completed',
    'https://via.placeholder.com/800x600/0066cc/ffffff?text=South+Forbes',
    '[]'::jsonb,
    'Private Client',
    false,
    true,
    '[{"title": "Luxury Villa", "description": "High-end residential construction"}, {"title": "Contemporary Design", "description": "Modern architectural elements"}]'::jsonb,
    '["Luxury villa construction", "Contemporary design", "High-end finishes"]'::jsonb,
    '["Residential", "Luxury", "Villa"]'::jsonb
);

-- 2. INSERT SAMPLE SERVICES
INSERT INTO services (
    title, description, icon, features, benefits, category, 
    is_featured, sort_order, published
) VALUES 
(
    'Commercial Construction',
    'Full-service commercial construction for retail spaces, offices, and industrial facilities.',
    'Building',
    '["Custom office buildings and retail spaces", "Industrial and manufacturing facilities", "Healthcare and institutional buildings"]'::jsonb,
    '["Increased property value", "Enhanced business operations", "Modern, energy-efficient facilities"]'::jsonb,
    'commercial',
    true,
    1,
    true
),
(
    'Residential Construction',
    'Quality home building services, from custom homes to multi-family residential projects.',
    'Home',
    '["Custom home design and construction", "Multi-family residential projects", "Luxury home construction"]'::jsonb,
    '["Personalized living spaces", "Energy-efficient designs", "Quality craftsmanship"]'::jsonb,
    'residential',
    true,
    2,
    true
),
(
    'Renovation',
    'Transform your existing space with our professional renovation services, from minor updates to complete makeovers.',
    'Home',
    '["Interior remodeling", "Exterior upgrades", "Space optimization", "Modernization"]'::jsonb,
    '["Enhanced functionality", "Improved aesthetics", "Increased property value"]'::jsonb,
    'specialized',
    false,
    3,
    true
),
(
    'Painting',
    'Professional painting services for both interior and exterior surfaces, using high-quality paints and techniques.',
    'PenTool',
    '["Interior painting", "Exterior painting", "Surface preparation", "Color consultation"]'::jsonb,
    '["Enhanced aesthetics", "Protection from elements", "Long-lasting finish"]'::jsonb,
    'specialized',
    false,
    4,
    true
);

-- 3. VERIFY DATA WAS INSERTED
SELECT 'Projects inserted:' as info, COUNT(*) as count FROM projects;
SELECT 'Services inserted:' as info, COUNT(*) as count FROM services;

-- Show sample data
SELECT 'Sample projects:' as info;
SELECT id, title, category, location FROM projects LIMIT 3;

SELECT 'Sample services:' as info;
SELECT id, title, category, is_featured FROM services LIMIT 3;

SELECT 'Data insertion completed successfully!' as status;
