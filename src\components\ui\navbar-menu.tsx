"use client";
import React, { useCallback, memo } from "react";
import { motion } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import { OptimizedImage } from '@/lib/imageUtils';

const transition = {
  type: "spring",
  mass: 0.4,
  damping: 10,
  stiffness: 80,
  restDelta: 0.01,
  restSpeed: 0.01,
};

export const MenuItem = memo(({
  setActive,
  active,
  item,
  children,
  to,
}: {
  setActive: (item: string | null) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
  to?: string;
}) => {
  const navigate = useNavigate();
  
  const handleClick = useCallback(() => {
    if (to) {
      navigate(to);
      setActive(null); // Close dropdown when navigating
    }
  }, [to, navigate, setActive]);

  const handleMouseEnter = useCallback(() => {
    setActive(item);
  }, [item, setActive]);

  return (
    <div 
      className="relative"
      onMouseEnter={handleMouseEnter}
    >
      <motion.p
        onClick={handleClick}
        transition={{ duration: 0.2 }}
        className="cursor-pointer hover:opacity-[0.9]"
      >
        {item}
      </motion.p>
      {active !== null && active === item && (
        <motion.div
          initial={{ opacity: 0, scale: 0.85, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={transition}
        >
          <div 
            className="absolute top-[calc(100%_+_1.2rem)] left-1/2 transform -translate-x-1/2 pt-4 dropdown-content"
          >
            <motion.div
              transition={transition}
              layoutId="active"
              className="bg-white backdrop-blur-sm rounded-xl overflow-hidden border border-black/[0.1] shadow-xl z-50"
            >
              <motion.div
                layout
                className="w-max h-full p-4"
                onClick={() => setActive(null)}
              >
                {children}
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </div>
  );
});

export const Menu = memo(({
  setActive,
  children,
  className,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <nav
      className={cn("relative rounded-full border border-transparent flex justify-center space-x-6 px-8 py-3 z-40", className)}
    >
      {children}
    </nav>
  );
});

export const ServiceItem = memo(({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  const navigate = useNavigate();
  
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    
    // For hash links within the same page
    if (href.includes('#')) {
      const [path, hash] = href.split('#');
      const currentPath = window.location.pathname;
      
      // If we're already on the correct page, just scroll to the element
      if ((path === '' || path === '/') && currentPath === '/') {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Navigate to new page with hash
        navigate(href);
        
        // Reduced timeout for better response
        setTimeout(() => {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }, 50);
      }
    } else {
      // Standard navigation without hash
      navigate(href);
    }
  }, [href, navigate]);
  
  return (
    <Link to={href} className="flex space-x-2" onClick={handleClick}>
      <OptimizedImage
        src={src}
        width={120}
        height={70}
        loading="lazy"
        alt={title}
        className="flex-shrink-0 rounded-md shadow-md object-cover h-[70px]"
      />
      <div>
        <h4 className="text-base font-bold mb-1 text-rcs-blue">
          {title}
        </h4>
        <p className="text-neutral-700 text-sm max-w-[12rem]">
          {description}
        </p>
      </div>
    </Link>
  );
});

export const HoveredLink = memo(({ children, to, ...rest }: {
  children: React.ReactNode;
  to: string;
  [key: string]: any;
}) => {
  const navigate = useNavigate();
  
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    
    // For hash links within the same page
    if (to.includes('#')) {
      const [path, hash] = to.split('#');
      const currentPath = window.location.pathname;
      
      // If we're already on the correct page, just scroll to the element
      if (path === '' || path === '/' && currentPath === '/') {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Navigate to new page with hash
        navigate(to);
        
        // Reduced timeout for better response
        setTimeout(() => {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }, 50);
      }
    } else {
      // Standard navigation without hash
      navigate(to);
    }
  }, [to, navigate]);
  
  return (
    <Link
      to={to}
      {...rest}
      onClick={handleClick}
      className="text-neutral-700 hover:text-rcs-gold transition-colors duration-200"
    >
      {children}
    </Link>
  );
});
