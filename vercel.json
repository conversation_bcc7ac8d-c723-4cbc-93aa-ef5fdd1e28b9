{"version": 2, "rewrites": [{"source": "/(.*)", "destination": "/"}, {"source": "/api/verify-recaptcha", "destination": "/api/verify-recaptcha.js"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "text/javascript; charset=utf-8"}]}, {"source": "/(.*)\\.mjs", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "text/javascript; charset=utf-8"}]}, {"source": "/(.*)\\.tsx", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "text/javascript; charset=utf-8"}]}, {"source": "/(.*)\\.ts", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "text/javascript; charset=utf-8"}]}, {"source": "/(.*)\\.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "cleanUrls": true}