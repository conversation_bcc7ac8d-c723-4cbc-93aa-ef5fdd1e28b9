-- =====================================================
-- SIMPLE DATABASE TEST - Run this first to see what's in your database
-- =====================================================

-- 1. Check if projects table exists and show all projects
SELECT 'All projects in database:' as info;
SELECT * FROM projects ORDER BY created_at DESC;

-- 2. Count total projects
SELECT 'Total projects count:' as info;
SELECT COUNT(*) as total_projects FROM projects;

-- 3. Check table structure
SELECT 'Projects table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position;

-- 4. Check if published column exists
SELECT 'Checking for published column:' as info;
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'projects' AND column_name = 'published';

-- 5. Show recent projects with all details
SELECT 'Recent projects with details:' as info;
SELECT id, title, category, location, status, created_at 
FROM projects 
ORDER BY created_at DESC 
LIMIT 10;
