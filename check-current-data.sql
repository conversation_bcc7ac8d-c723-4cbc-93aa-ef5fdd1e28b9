-- =====================================================
-- CHECK CURRENT DATABASE DATA
-- Run this to see what's currently in your database
-- =====================================================

-- 1. Count projects
SELECT 'Total projects:' as info, COUNT(*) as count FROM projects;

-- 2. Show all projects with basic info
SELECT 'All projects:' as info;
SELECT id, title, category, location, published, created_at 
FROM projects 
ORDER BY created_at DESC;

-- 3. Count services
SELECT 'Total services:' as info, COUNT(*) as count FROM services;

-- 4. Show all services
SELECT 'All services:' as info;
SELECT id, title, category, is_featured, created_at 
FROM services 
ORDER BY created_at DESC;

-- 5. Check table structures
SELECT 'Projects table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position;

SELECT 'Services table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'services' 
ORDER BY ordinal_position;
