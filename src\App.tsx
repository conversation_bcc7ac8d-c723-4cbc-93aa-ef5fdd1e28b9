import React, { Suspense, lazy, useEffect, memo } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import Layout from "./components/Layout";
import PageTransition from "./components/PageTransition";
import { AuthProvider } from "./contexts/AuthContext";

// Optimized loading with error handling
const loadComponent = (componentImport: () => Promise<any>) => {
  return lazy(() => 
    componentImport().catch(error => {
      console.error("Failed to load component:", error);
      return { default: () => <FallbackError /> };
    })
  );
};

// Lazy load pages with optimized error handling
const Index = loadComponent(() => import("./pages/Index"));
const Services = loadComponent(() => import("./pages/Services"));
const Projects = loadComponent(() => import("./pages/Projects"));
const Contact = loadComponent(() => import("./pages/Contact"));
const NotFound = loadComponent(() => import("./pages/NotFound"));
const ChatBot = loadComponent(() => import("./components/ChatBot"));

// Lazy load admin pages
const AdminLogin = loadComponent(() => import("./pages/admin/Login"));
const AdminDashboard = loadComponent(() => import("./pages/admin/Dashboard"));
const AdminProjects = loadComponent(() => import("./pages/admin/Projects"));
const AdminServices = loadComponent(() => import("./pages/admin/Services"));
const AdminCompanyInfo = loadComponent(() => import("./pages/admin/CompanyInfo"));
const AdminBlog = loadComponent(() => import("./pages/admin/Blog"));
const AdminMedia = loadComponent(() => import("./pages/admin/Media"));
const AdminAnalytics = loadComponent(() => import("./pages/admin/Analytics"));
const AdminTestDatabase = loadComponent(() => import("./pages/admin/TestDatabase"));
const AdminQuickTest = loadComponent(() => import("./pages/admin/QuickTest"));
const AdminSimpleTest = loadComponent(() => import("./pages/admin/SimpleTest"));
const TestConnection = loadComponent(() => import("./pages/TestConnection"));
const AdminLayout = loadComponent(() => import("./components/admin/AdminLayout"));
const ProtectedRoute = loadComponent(() => import("./components/admin/ProtectedRoute"));

// Loading fallback component
const LoadingFallback = memo(() => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-rcs-blue"></div>
  </div>
));

// Error fallback component
const FallbackError = memo(() => (
  <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
    <h2 className="text-xl font-bold text-red-600 mb-2">Failed to load component</h2>
    <p className="mb-4">Please try refreshing the page</p>
    <button 
      onClick={() => window.location.reload()} 
      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
    >
      Refresh
    </button>
  </div>
));

// Create QueryClient with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

// AnimatedRoutes component with optimized rendering
const AnimatedRoutes = memo(() => {
  const location = useLocation();
  
  useEffect(() => {
    // Scroll to top on route change with smooth scrolling
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    // Preload the next potential routes on initial page load
    const preloadRoutes = () => {
      // Only preload in production to save dev resources
      if (process.env.NODE_ENV === 'production') {
        // Use setTimeout to not block the main thread
        setTimeout(() => {
          // Preload all routes after page load
          import("./pages/Index");
          import("./pages/Services");
          import("./pages/Projects");
          import("./pages/Contact");
        }, 2000); // Wait 2 seconds after initial load
      }
    };
    
    // Only run preloading on first mount
    if (location.pathname === '/') {
      preloadRoutes();
    }
  }, [location.pathname]);
  
  return (
    <AnimatePresence mode="wait">
      <Suspense fallback={<LoadingFallback />}>
        <Routes location={location} key={location.pathname}>
          <Route path="/" element={<PageTransition><Index /></PageTransition>} />
          <Route path="/services" element={<PageTransition><Services /></PageTransition>} />
          <Route path="/projects" element={<PageTransition><Projects /></PageTransition>} />
          <Route path="/contact" element={<PageTransition><Contact /></PageTransition>} />
          <Route path="/test-connection" element={<PageTransition><TestConnection /></PageTransition>} />
          <Route path="*" element={<PageTransition><NotFound /></PageTransition>} />
        </Routes>
      </Suspense>
    </AnimatePresence>
  );
});

// Admin routes component - using Outlet pattern
const AdminRoutes = memo(() => {
  return <AdminLayout />;
});

// Main App component with optimized structure
const App = memo(() => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <BrowserRouter
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}
          >
            <Routes>
              {/* Admin routes - no main layout */}
              <Route path="/admin/login" element={<AdminLogin />} />
              <Route path="/admin" element={
                <ProtectedRoute>
                  <AdminRoutes />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="" element={<AdminDashboard />} />
                <Route path="projects" element={<AdminProjects />} />
                <Route path="projects/new" element={<AdminProjects />} />
                <Route path="services" element={<AdminServices />} />
                <Route path="services/new" element={<AdminServices />} />
                <Route path="company" element={<AdminCompanyInfo />} />
                <Route path="blog" element={<AdminBlog />} />
                <Route path="blog/new" element={<AdminBlog />} />
                <Route path="media" element={<AdminMedia />} />
                <Route path="analytics" element={<AdminAnalytics />} />
                <Route path="test" element={<AdminTestDatabase />} />
                <Route path="quicktest" element={<AdminQuickTest />} />
                <Route path="simpletest" element={<AdminSimpleTest />} />
              </Route>

              {/* Public routes - with main layout */}
              <Route path="/*" element={
                <Layout>
                  <AnimatedRoutes />
                </Layout>
              } />
            </Routes>

            <Toaster />
            <Sonner />
            {/* ChatBot is lazy loaded and only shown when ready */}
            <Suspense fallback={null}>
              <ChatBot />
            </Suspense>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
});

export default App;
