import React, { useState, useEffect } from 'react';
import { Upload, Search, Trash2, Download, Eye, Grid, List, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/database';
import { FileUpload } from '@/components/admin/FileUpload';
import { SimpleStorageService as StorageService, UploadResult } from '@/lib/storage-simple';
import type { MediaFile, MediaFileInsert } from '@/lib/database.types';

interface MediaGridProps {
  files: MediaFile[];
  onDelete: (id: string) => void;
  onView: (file: MediaFile) => void;
  viewMode: 'grid' | 'list';
}

const MediaGrid: React.FC<MediaGridProps> = ({ files, onDelete, onView, viewMode }) => {
  if (viewMode === 'list') {
    return (
      <div className="space-y-2">
        {files.map((file) => (
          <Card key={file.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {file.mime_type?.startsWith('image/') ? (
                    <img
                      src={StorageService.getPublicUrl(file.file_path)}
                      alt={file.alt_text || file.filename}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-gray-400 text-xs text-center">
                      {file.mime_type?.split('/')[1]?.toUpperCase()}
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className="font-medium">{file.original_name || file.filename}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>{file.mime_type}</span>
                    <span>{file.file_size ? `${(file.file_size / 1024).toFixed(1)} KB` : 'Unknown size'}</span>
                    <span>{new Date(file.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => onView(file)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => onDelete(file.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {files.map((file) => (
        <Card key={file.id} className="group cursor-pointer" onClick={() => onView(file)}>
          <CardContent className="p-2">
            <div className="aspect-square bg-gray-100 rounded-lg mb-2 overflow-hidden relative">
              {file.mime_type?.startsWith('image/') ? (
                <img
                  src={StorageService.getPublicUrl(file.file_path)}
                  alt={file.alt_text || file.filename}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <div className="text-2xl mb-1">📄</div>
                    <div className="text-xs">{file.mime_type?.split('/')[1]?.toUpperCase()}</div>
                  </div>
                </div>
              )}
              
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(file.id);
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="text-xs text-gray-600 truncate">
              {file.original_name || file.filename}
            </div>
            <div className="text-xs text-gray-400">
              {file.file_size ? `${(file.file_size / 1024).toFixed(1)} KB` : 'Unknown'}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const MediaAdmin = () => {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const filesData = await db.mediaFiles.getAll();
      setFiles(filesData);
    } catch (error) {
      console.error('Error fetching files:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch media files',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (results: UploadResult[]) => {
    try {
      for (const result of results) {
        if (result.url) {
          const fileData: MediaFileInsert = {
            filename: result.path.split('/').pop() || 'unknown',
            original_name: result.path.split('/').pop() || 'unknown',
            file_path: result.path,
            file_size: null, // We don't have size info from upload result
            mime_type: null, // We don't have mime type info from upload result
            alt_text: null
          };
          
          await db.mediaFiles.create(fileData);
        }
      }
      
      toast({
        title: 'Success',
        description: `${results.length} file(s) uploaded successfully`
      });
      
      setIsUploadDialogOpen(false);
      fetchFiles();
    } catch (error) {
      console.error('Error saving file records:', error);
      toast({
        title: 'Error',
        description: 'Failed to save file records',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this file?')) {
      try {
        const file = files.find(f => f.id === id);
        if (file) {
          // Delete from storage
          await StorageService.deleteFile(file.file_path);
          // Delete from database
          await db.mediaFiles.delete(id);
          
          toast({
            title: 'Success',
            description: 'File deleted successfully'
          });
          
          fetchFiles();
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete file',
          variant: 'destructive'
        });
      }
    }
  };

  const handleView = (file: MediaFile) => {
    setSelectedFile(file);
  };

  const downloadFile = (file: MediaFile) => {
    const url = StorageService.getPublicUrl(file.file_path);
    const link = document.createElement('a');
    link.href = url;
    link.download = file.original_name || file.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = (file.original_name || file.filename).toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.alt_text?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || 
                       (typeFilter === 'images' && file.mime_type?.startsWith('image/')) ||
                       (typeFilter === 'documents' && !file.mime_type?.startsWith('image/'));
    
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Media Library</h1>
          <p className="text-gray-600">Manage your uploaded files and images</p>
        </div>
        
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Files
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Media Files</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <FileUpload
                onUpload={handleUpload}
                multiple
                folder="media"
                maxFiles={20}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Files</SelectItem>
            <SelectItem value="images">Images</SelectItem>
            <SelectItem value="documents">Documents</SelectItem>
          </SelectContent>
        </Select>
        
        <div className="flex border rounded-lg">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Files Grid/List */}
      {filteredFiles.length > 0 ? (
        <MediaGrid
          files={filteredFiles}
          onDelete={handleDelete}
          onView={handleView}
          viewMode={viewMode}
        />
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">No media files found</p>
            <Button onClick={() => setIsUploadDialogOpen(true)}>
              Upload your first file
            </Button>
          </CardContent>
        </Card>
      )}

      {/* File Preview Dialog */}
      {selectedFile && (
        <Dialog open={!!selectedFile} onOpenChange={() => setSelectedFile(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{selectedFile.original_name || selectedFile.filename}</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              {selectedFile.mime_type?.startsWith('image/') ? (
                <img
                  src={StorageService.getPublicUrl(selectedFile.file_path)}
                  alt={selectedFile.alt_text || selectedFile.filename}
                  className="w-full max-h-96 object-contain rounded-lg"
                />
              ) : (
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <div className="text-4xl mb-4">📄</div>
                  <p className="text-gray-600">File preview not available</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>File Type:</strong> {selectedFile.mime_type || 'Unknown'}
                </div>
                <div>
                  <strong>File Size:</strong> {selectedFile.file_size ? `${(selectedFile.file_size / 1024).toFixed(1)} KB` : 'Unknown'}
                </div>
                <div>
                  <strong>Uploaded:</strong> {new Date(selectedFile.created_at).toLocaleDateString()}
                </div>
                <div>
                  <strong>Path:</strong> {selectedFile.file_path}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => downloadFile(selectedFile)}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button variant="destructive" onClick={() => {
                  handleDelete(selectedFile.id);
                  setSelectedFile(null);
                }}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default MediaAdmin;
