import React, { memo } from 'react';
import { motion } from 'framer-motion';

interface PageTransitionProps {
  children: React.ReactNode;
}

// Simplified animation variants with reduced complexity
const pageVariants = {
  initial: {
    opacity: 0,
    y: 5
  },
  in: {
    opacity: 1,
    y: 0
  },
  out: {
    opacity: 0,
    y: -5
  }
};

// Simpler, faster transition for better performance
const pageTransition = {
  type: 'tween',
  ease: 'easeInOut',
  duration: 0.3
};

// Memoize component to prevent unnecessary re-renders
const PageTransition: React.FC<PageTransitionProps> = memo(({ children }) => {
  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
      className="w-full"
    >
      {children}
    </motion.div>
  );
});

export default PageTransition;
