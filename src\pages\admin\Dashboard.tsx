import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Building2,
  Wrench,
  FileText,
  Image,
  TrendingUp,
  Users,
  Calendar,
  Activity,
  Plus,
  Eye,
  Edit,
  BarChart3,
  Database,
  Download
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions, Permission } from '@/lib/permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { runMigrationsFromAdmin } from '@/lib/migrations/run-migrations';

interface StatCard {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ size?: number; className?: string }>;
  href?: string;
  permission?: Permission;
}

interface RecentActivity {
  id: string;
  type: 'project' | 'service' | 'blog' | 'media';
  action: 'created' | 'updated' | 'published' | 'deleted';
  title: string;
  timestamp: Date;
  user: string;
}

interface QuickAction {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  permission?: Permission;
  variant?: 'default' | 'secondary' | 'outline';
}

const AdminDashboard: React.FC = () => {
  const { adminUser } = useAuth();
  const { hasPermission } = usePermissions();
  const [stats, setStats] = useState<StatCard[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [migrating, setMigrating] = useState(false);
  const { toast } = useToast();

  // Mock data - replace with actual API calls
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock statistics
        const mockStats: StatCard[] = [
          {
            title: 'Total Projects',
            value: 24,
            change: '+12%',
            changeType: 'positive',
            icon: Building2,
            href: '/admin/projects',
            permission: Permission.VIEW_PROJECTS,
          },
          {
            title: 'Active Services',
            value: 8,
            change: '+2',
            changeType: 'positive',
            icon: Wrench,
            href: '/admin/services',
            permission: Permission.VIEW_SERVICES,
          },
          {
            title: 'Blog Posts',
            value: 15,
            change: '+3',
            changeType: 'positive',
            icon: FileText,
            href: '/admin/blog',
            permission: Permission.VIEW_BLOG_POSTS,
          },
          {
            title: 'Media Files',
            value: 156,
            change: '+24',
            changeType: 'positive',
            icon: Image,
            href: '/admin/media',
            permission: Permission.VIEW_MEDIA,
          },
        ];

        // Mock recent activity
        const mockActivity: RecentActivity[] = [
          {
            id: '1',
            type: 'project',
            action: 'created',
            title: 'New Residential Project in Cavite',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            user: 'Admin',
          },
          {
            id: '2',
            type: 'blog',
            action: 'published',
            title: 'Construction Safety Guidelines',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
            user: 'Admin',
          },
          {
            id: '3',
            type: 'service',
            action: 'updated',
            title: 'Commercial Construction Service',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
            user: 'Admin',
          },
          {
            id: '4',
            type: 'media',
            action: 'created',
            title: 'Project Gallery Images',
            timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
            user: 'Admin',
          },
        ];

        setStats(mockStats);
        setRecentActivity(mockActivity);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Handle data migration
  const handleMigration = async () => {
    setMigrating(true);
    try {
      const result = await runMigrationsFromAdmin();

      if (result.success) {
        toast({
          title: 'Migration Successful',
          description: result.message,
        });
      } else {
        toast({
          title: 'Migration Failed',
          description: result.message,
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Migration Error',
        description: 'An unexpected error occurred during migration',
        variant: 'destructive'
      });
    } finally {
      setMigrating(false);
    }
  };

  // Quick actions
  const quickActions: QuickAction[] = [
    {
      title: 'Add New Project',
      description: 'Create a new construction project',
      href: '/admin/projects',
      icon: Building2,
      permission: Permission.CREATE_PROJECTS,
    },
    {
      title: 'Add Service',
      description: 'Add a new service offering',
      href: '/admin/services',
      icon: Wrench,
      permission: Permission.CREATE_SERVICES,
      variant: 'secondary',
    },
    {
      title: 'Manage Company Info',
      description: 'Update company information',
      href: '/admin/company',
      icon: Building2,
      permission: Permission.VIEW_COMPANY_INFO,
      variant: 'outline',
    },
    {
      title: 'Upload Media',
      description: 'Add images and files',
      href: '/admin/media',
      icon: Image,
      permission: Permission.UPLOAD_MEDIA,
      variant: 'outline',
    },
  ];

  // Format relative time
  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  // Get activity icon
  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'project': return Building2;
      case 'service': return Wrench;
      case 'blog': return FileText;
      case 'media': return Image;
      default: return Activity;
    }
  };

  // Get activity color
  const getActivityColor = (action: RecentActivity['action']) => {
    switch (action) {
      case 'created': return 'bg-green-100 text-green-800';
      case 'updated': return 'bg-blue-100 text-blue-800';
      case 'published': return 'bg-purple-100 text-purple-800';
      case 'deleted': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-rcs-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-rcs-blue to-rcs-muted-blue rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {adminUser?.email?.split('@')[0] || 'Admin'}!
        </h1>
        <p className="text-blue-100">
          Here's what's happening with your construction business today.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          // Check permissions
          if (stat.permission && !hasPermission(stat.permission)) {
            return null;
          }

          const StatIcon = stat.icon;
          
          return (
            <Card key={stat.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <StatIcon className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                {stat.change && (
                  <p className={`text-xs flex items-center mt-1 ${
                    stat.changeType === 'positive' ? 'text-green-600' : 
                    stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {stat.change} from last month
                  </p>
                )}
                {stat.href && (
                  <Link 
                    to={stat.href}
                    className="text-xs text-rcs-blue hover:underline mt-2 inline-block"
                  >
                    View all →
                  </Link>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common tasks to manage your content
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {quickActions.map((action) => {
              // Check permissions
              if (action.permission && !hasPermission(action.permission)) {
                return null;
              }

              const ActionIcon = action.icon;
              
              return (
                <Link key={action.title} to={action.href}>
                  <Button 
                    variant={action.variant || 'default'} 
                    className="w-full justify-start h-auto p-4"
                  >
                    <ActionIcon className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-sm opacity-70">{action.description}</div>
                    </div>
                  </Button>
                </Link>
              );
            })}

            {/* Migration Button */}
            <div className="pt-4 border-t">
              <Button
                onClick={handleMigration}
                disabled={migrating}
                variant="outline"
                className="w-full justify-start h-auto p-4 border-dashed"
              >
                <Database className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <div className="font-medium">
                    {migrating ? 'Migrating Data...' : 'Migrate Existing Data'}
                  </div>
                  <div className="text-sm opacity-70">
                    Transfer projects and services to database
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest changes to your content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => {
                const ActivityIcon = getActivityIcon(activity.type);
                
                return (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <ActivityIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getActivityColor(activity.action)}`}
                        >
                          {activity.action}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {formatRelativeTime(activity.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <Link 
                to="/admin/activity" 
                className="text-sm text-rcs-blue hover:underline"
              >
                View all activity →
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
