import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { Plugin } from 'vite'

// Custom plugin to fix the MIME type of data URIs for JavaScript modules
const dataURIJavaScriptPlugin = (): Plugin => {
  return {
    name: 'data-uri-javascript-fix',
    transformIndexHtml(html) {
      // Replace incorrect MIME type in data URIs with correct one for JavaScript modules
      html = html.replace(
        /data:application\/octet-stream;base64,/g, 
        'data:text/javascript;base64,'
      );
      
      // Also ensure .tsx files have the correct MIME type
      html = html.replace(
        /href="(.*\.tsx)"/g, 
        'href="$1" type="text/javascript"'
      );
      
      return html;
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Fix MIME type for data URIs
    dataURIJavaScriptPlugin(),
    // Bundle analyzer - generates stats.html
    visualizer({
      open: false, // Set to true to open the stats file after build
      gzipSize: true,
      brotliSize: true,
      filename: 'stats.html'
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  // Optimizations for build
  build: {
    // Use Terser for better minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
      format: {
        comments: false,
      },
    },
    // Ensure all extensions get compiled to JS
    assetsInlineLimit: 4096,
    rollupOptions: {
      output: {
        manualChunks: {
          // Split framework code
          vendor: [
            'react', 
            'react-dom', 
            'react-router-dom', 
            '@tanstack/react-query',
            'framer-motion'
          ],
          // Split UI components
          ui: [
            './src/components/ui/toaster',
            './src/components/ui/tooltip',
            './src/components/ui/navbar-menu',
            './src/components/ui/tabs',
            './src/components/ui/accordion'
          ],
          // Utilities
          utils: [
            './src/lib/utils',
            './src/lib/performance',
            './src/lib/imageUtils'
          ]
        },
        // Optimize CSS and handle file extensions correctly
        assetFileNames: (assetInfo) => {
          // Handle undefined case
          if (!assetInfo.name) return 'assets/[name]-[hash][extname]';
          
          const info = assetInfo.name.split('.');
          const extType = info.length > 1 ? info.pop() : '';
          
          if (!extType) return 'assets/[name]-[hash][extname]';
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico|webp/i.test(extType)) {
            return `assets/img/[name]-[hash][extname]`;
          } 
          
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/[name]-[hash][extname]`;
          }
          
          if (/tsx?/i.test(extType)) {
            return `assets/js/[name]-[hash].js`;
          }
          
          return `assets/${extType}/[name]-[hash][extname]`;
        },
        // Optimize JS chunking
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
      },
    },
    // Increase warning limit for chunk size
    chunkSizeWarningLimit: 1000,
    // Generate compressed files
    outDir: 'dist',
    assetsDir: 'assets',
    // Source maps for debugging (can be removed for production)
    sourcemap: false,
    // Optimizing for larger projects
    target: 'es2015',
    cssCodeSplit: true,
    modulePreload: true,
    reportCompressedSize: false,
  },
  // Development server options
  server: {
    open: true,
    host: true,
    port: 3000,
    strictPort: false,
    hmr: {
      overlay: true,
    },
    // Optimize cache during development
    watch: {
      usePolling: false,
      interval: 100
    }
  },
  // Optimize deps for faster startup
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'framer-motion'
    ],
    exclude: [],
  },
  // Enable brotli/gzip compression in preview mode
  preview: {
    port: 4173,
    strictPort: false,
    host: true,
    open: true
  }
})
