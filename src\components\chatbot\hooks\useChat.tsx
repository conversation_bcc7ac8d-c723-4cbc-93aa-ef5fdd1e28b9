import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Message, ConversationContext } from '../types';
import { extractContextFromMessage } from '../chatUtils';
import { processCalculation } from '../chatUtils';
import { fetchChatResponse } from '../api';

// Define conversation history type
interface ConversationHistory {
  id: string;
  title: string;
  timestamp: Date;
  messages: Message[];
  context: ConversationContext;
}

export const useChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    { 
      content: "Welcome to Rodelas Construction Services! I'm your AI assistant. How can I help you with your construction needs today?", 
      isBot: true, 
      timestamp: new Date(),
      buttons: [
        { text: "Services", action: "What are your services?", path: "/services" },
        { text: "Get Quote", action: "I need a quote", path: "/contact" },
        { text: "Projects", action: "Show me your projects", path: "/projects" }
      ]
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversationContext, setConversationContext] = useState<ConversationContext>({});
  const [showHistory, setShowHistory] = useState(false);
  const [savedConversations, setSavedConversations] = useState<ConversationHistory[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatBoxRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Use environment variable for API key instead of hardcoding
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY || "";
  
  // Submit contact form data using EmailJS
  const submitContactForm = async (contactData: {
    name: string;
    email: string;
    phone: string;
    message: string;
    csrfToken?: string;
  }) => {
    try {
      // Import the sendContactEmail function from email-service
      const { sendContactEmail } = await import('@/lib/email-service');
      
      // Import CSRF token if not provided
      const { getCsrfToken } = await import('@/lib/csrf-protection');
      
      // Convert the data to match ContactFormData interface
      const formData = {
        from_name: contactData.name,
        from_email: contactData.email,
        phone: contactData.phone,
        message: contactData.message,
        subject: 'New Inquiry from Chatbot',
        csrfToken: contactData.csrfToken || getCsrfToken() // Use provided token or get a new one
      };
      
      // Send the email using EmailJS
      const result = await sendContactEmail(formData);
      
      return result.success;
    } catch (error) {
      console.error('Error submitting contact form:', error);
      return false;
    }
  };
  
  // Load saved conversations from localStorage
  useEffect(() => {
    try {
      const savedConvs = localStorage.getItem('rcs_saved_conversations');
      if (savedConvs) {
        const parsed = JSON.parse(savedConvs).map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }));
        setSavedConversations(parsed);
      }
    } catch (error) {
      console.error('Error loading saved conversations:', error);
    }
  }, []);
  
  // Save conversations to localStorage when they change
  useEffect(() => {
    if (savedConversations.length > 0) {
      try {
        localStorage.setItem('rcs_saved_conversations', JSON.stringify(savedConversations));
      } catch (error) {
        console.error('Error saving conversations:', error);
      }
    }
  }, [savedConversations]);
  
  const toggleChat = () => {
    if (!isOpen) {
      setIsOpen(true);
      setIsMinimized(false);
      // Focus input field when chat is opened
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      setIsMinimized(!isMinimized);
      if (!isMinimized) {
        // Focus input field when chat is maximized
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const closeChat = () => {
    setIsOpen(false);
  };

  const toggleHistory = () => {
    setShowHistory(prev => !prev);
  };

  // Save current conversation
  const saveCurrentConversation = () => {
    if (messages.length <= 1) return; // Don't save if only welcome message exists
    
    const firstUserMessage = messages.find(msg => !msg.isBot);
    const title = firstUserMessage 
      ? firstUserMessage.content.substring(0, 40) + (firstUserMessage.content.length > 40 ? '...' : '')
      : 'New Conversation';
    
    const newConversation: ConversationHistory = {
      id: currentConversationId || Date.now().toString(),
      title,
      timestamp: new Date(),
      messages: [...messages],
      context: {...conversationContext}
    };
    
    setSavedConversations(prev => {
      // Replace if exists, otherwise add
      const exists = prev.findIndex(c => c.id === newConversation.id);
      if (exists >= 0) {
        const updated = [...prev];
        updated[exists] = newConversation;
        return updated;
      }
      return [...prev, newConversation];
    });
    
    setCurrentConversationId(newConversation.id);
    return newConversation.id;
  };

  // Load a saved conversation
  const loadConversation = (id: string) => {
    const conversation = savedConversations.find(c => c.id === id);
    if (!conversation) return;
    
    setMessages(conversation.messages);
    setConversationContext(conversation.context);
    setCurrentConversationId(conversation.id);
    setShowHistory(false);
  };

  // Start a new conversation
  const startNewConversation = () => {
    // Save current if needed
    if (messages.length > 1) {
      saveCurrentConversation();
    }
    
    // Reset to initial state
    setMessages([
      { 
        content: "Welcome to Rodelas Construction Services! I'm your AI assistant. How can I help you with your construction needs today?", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "Services", action: "What are your services?", path: "/services" },
          { text: "Get Quote", action: "I need a quote", path: "/contact" },
          { text: "Projects", action: "Show me your projects", path: "/projects" }
        ]
      }
    ]);
    setConversationContext({});
    setCurrentConversationId(null);
    setShowHistory(false);
  };

  // Delete a saved conversation
  const deleteConversation = (id: string) => {
    setSavedConversations(prev => prev.filter(c => c.id !== id));
    if (currentConversationId === id) {
      startNewConversation();
    }
  };

  const handleButtonClick = (action: string, path?: string) => {
    const userMessage = { content: action, isBot: false, timestamp: new Date() };
    setMessages(prev => [...prev, userMessage]);
    
    // Special handling for contact collection through conversation
    if (action === "quick_contact_form" || action === "I want to submit my contact information" || action === "I want to submit my details") {
      // Redirect to the contact page instead
      setTimeout(() => {
        setMessages(prev => [...prev, { 
          content: "Please visit our contact page to fill out the form. I'll redirect you there now.", 
          isBot: true, 
          timestamp: new Date()
        }]);
        
        // Navigate to contact page after a short delay
        setTimeout(() => {
          navigate('/contact');
          closeChat();
        }, 2000);
      }, 500);
      return;
    }
    
    // If we have a path, check for specific destinations to handle differently
    if (path) {
      let responseContent = "";
      
      // Set appropriate response content based on path
      if (path === "/projects") {
        responseContent = "Here are our completed projects that showcase our expertise in construction. You can browse through our residential, commercial, and renovation projects in the gallery. Each project includes details about the scope of work, location, and features. Feel free to explore different categories to see our diverse range of construction capabilities.";
      } 
      else if (path === "/services") {
        responseContent = "We offer a comprehensive range of construction services including commercial construction, residential building, renovation and remodeling, specialized technical services like carpentry and electrical work, and full construction management. You can explore each service in detail on this page to learn more about our expertise and how we can help with your specific needs.";
      }
      else if (path === "/contact") {
        responseContent = "You can reach us via phone at 09670598903/09951858305, office line ************, or <NAME_EMAIL>. Our office is located at Block 8 Lot 7 Phase 2 Gregory Street, St. Joseph Village, 7 Marinig, Cabuyao, Laguna. Please fill out the contact form with details about your project for a personalized quote.";
      }
      
      // If we have a specific response, send it
      if (responseContent) {
        // First show a transition message
        setMessages(prev => [...prev, { 
          content: path === "/projects" 
            ? "Great! I'm redirecting you to our projects page where you can view our portfolio." 
            : path === "/services" 
              ? "I'm taking you to our services page where you can explore what we offer."
              : "I'm directing you to our contact page for quotes and inquiries.", 
          isBot: true, 
          timestamp: new Date()
        }]);
        
        // Navigate to the page
        setTimeout(() => {
          navigate(path);
          
          // After navigation, add the detailed response
          setTimeout(() => {
            setMessages(prev => [...prev, { 
              content: responseContent, 
              isBot: true, 
              timestamp: new Date(),
              buttons: path === "/projects" 
                ? [
                    { text: "Get Quote", action: "I want a quote for a similar project", path: "/contact" },
                    { text: "View Services", action: "What are your services?", path: "/services" }
                  ]
                : path === "/services"
                  ? [
                      { text: "Get Quote", action: "I need a quote", path: "/contact" },
                      { text: "View Projects", action: "Show me your projects", path: "/projects" }
                    ]
                  : [
                      { text: "View Services", action: "What are your services?", path: "/services" },
                      { text: "View Projects", action: "Show me your projects", path: "/projects" }
                    ]
            }]);
          }, 1000); // Add the detailed message 1 second after navigation
        }, 1000);
        
        return;
      }
    }
    
    // Otherwise process as normal query
    handleUserQuery(action);
  };

  const handleUserQuery = async (userQuery: string) => {
    setIsLoading(true);
    
    // Auto-save conversation if it has content
    if (messages.length > 1) {
      saveCurrentConversation();
    }

    // Check if we're in contact collection mode
    if (conversationContext.collectingContact) {
      // Process different stages of contact collection
      if (conversationContext.contactStage === 'name') {
        // Store the name and ask for email
        setConversationContext(prev => ({
          ...prev,
          contactName: userQuery,
          contactStage: 'email'
        }));
        
        setMessages(prev => [...prev, { 
          content: `Thank you ${userQuery}. Now, please provide your email address so we can get back to you.`, 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Skip Contact Form", action: "Take me to the contact page", path: "/contact" }
          ]
        }]);
        
        setIsLoading(false);
        return;
      }
      
      else if (conversationContext.contactStage === 'email') {
        // Import email validator function
        const { validateEmail } = await import('@/lib/email-service');
        
        // First check basic format before doing more comprehensive validation
        const basicEmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!basicEmailRegex.test(userQuery)) {
          setMessages(prev => [...prev, { 
            content: "Mukhang hindi valid yang email address na yan. Please provide a valid email with the correct format (<EMAIL>).", 
            isBot: true, 
            timestamp: new Date()
          }]);
          
          setIsLoading(false);
          return;
        }
        
        // Comprehensive validation
        if (!validateEmail(userQuery)) {
          const domain = userQuery.split('@')[1].toLowerCase();
          let errorMsg = "Hindi namin ma-verify yang email address. ";
          
          // Provide more specific feedback based on domain
          if (domain.includes('tempmail') || domain.includes('mailinator') || 
              domain.includes('yopmail') || domain.includes('guerrilla')) {
            errorMsg += "Hindi po kami tumatanggap ng disposable o temporary email addresses. Please provide your regular email address.";
          } else if (!["com", "net", "org", "edu", "gov", "ph"].includes(domain.split('.').pop() || '')) {
            errorMsg += "Hindi supported ang domain extension na ginamit ninyo. Please use a common domain like .com, .net, .org, or .ph.";
          } else {
            errorMsg += "Siguraduhin po na tama ang spelling at valid na email address ang ibibigay. We need this to contact you regarding your inquiry.";
          }
          
          setMessages(prev => [...prev, { 
            content: errorMsg, 
            isBot: true, 
            timestamp: new Date()
          }]);
          
          setIsLoading(false);
          return;
        }
        
        // Store the email and ask for phone
        setConversationContext(prev => ({
          ...prev,
          contactEmail: userQuery,
          contactStage: 'phone'
        }));
        
        setMessages(prev => [...prev, { 
          content: "Great! Now, please provide your phone number (optional, you can type 'skip' to move to the next step).", 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Skip", action: "skip" }
          ]
        }]);
        
        setIsLoading(false);
        return;
      }
      
      else if (conversationContext.contactStage === 'phone') {
        // Store the phone and ask for message
        setConversationContext(prev => ({
          ...prev,
          contactPhone: userQuery === 'skip' ? 'Not provided' : userQuery,
          contactStage: 'message'
        }));
        
        setMessages(prev => [...prev, { 
          content: "Almost done! Please tell me briefly about your project or inquiry.", 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Skip", action: "skip" }
          ]
        }]);
        
        setIsLoading(false);
        return;
      }
      
      else if (conversationContext.contactStage === 'message') {
        // Store the message and complete the form
        const contactDetails = {
          name: conversationContext.contactName || 'Not provided',
          email: conversationContext.contactEmail || 'Not provided',
          phone: conversationContext.contactPhone || 'Not provided',
          message: userQuery === 'skip' ? 'Not provided' : userQuery
        };
        
        // Try to submit the form directly
        const submitted = await submitContactForm(contactDetails);
        
        // Format the contact summary with appropriate message
        const contactSummary = `
Thank you for providing your contact information!

💼 Name: ${contactDetails.name}
📧 Email: ${contactDetails.email}
📱 Phone: ${contactDetails.phone}
💬 Project Details: ${contactDetails.message}

${submitted 
  ? "✅ Your information has been sent to our team! We'll contact you shortly." 
  : "I'm redirecting you to our contact page to confirm your submission."}
        `;
        
        setMessages(prev => [...prev, { 
          content: contactSummary, 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Go to Contact Page", action: "View contact page", path: "/contact" }
          ]
        }]);
        
        // Reset contact collection
        setConversationContext(prev => ({
          ...prev,
          collectingContact: false,
          contactStage: null
        }));
        
        // Actually send the data to the contact page with parameters
        setTimeout(() => {
          // Create query parameters from the contact info to pre-fill the contact form
          const queryParams = new URLSearchParams({
            name: contactDetails.name,
            email: contactDetails.email,
            phone: contactDetails.phone,
            message: contactDetails.message,
            source: 'chatbot'
          }).toString();
          
          // Navigate to contact page with the query parameters
          navigate(`/contact?${queryParams}`);
        }, 3000);
        
        setIsLoading(false);
        return;
      }
    }

    // Extract context from the user message
    const contextUpdates = extractContextFromMessage(userQuery);
    
    // Update conversation context with extracted information
    if (Object.keys(contextUpdates).length > 0) {
      setConversationContext(prev => ({
        ...prev,
        ...contextUpdates
      }));
    }

    // PROJECT-RELATED QUERIES - Provide info without auto-redirect
    if ((userQuery === "Show me your projects" || 
        userQuery === "View Projects") ||
        (userQuery.length < 50 && ( // Only apply pattern matching on short messages
            userQuery.toLowerCase().match(/\bproject\b/i) ||
            userQuery.toLowerCase().match(/\bportfolio\b/i) ||
            userQuery.toLowerCase().match(/\bshow me your work\b/i) ||
            userQuery.toLowerCase().match(/\bshow.*project\b/i) ||
            userQuery.toLowerCase().match(/\bsee.*project\b/i) ||
            userQuery.toLowerCase().match(/\bview.*project\b/i) ||
            userQuery.toLowerCase().match(/\bpakita.*project\b/i) ||
            userQuery.toLowerCase().match(/\bpakita.*proyekto\b/i) ||
            userQuery.toLowerCase().match(/\bpkita.*project\b/i) || 
            userQuery.toLowerCase().match(/\bpkita.*mo\b/i) ||
            userQuery.toLowerCase().match(/\bipakita.*project\b/i) ||
            userQuery.toLowerCase().match(/\bano.*project\b/i) ||
            userQuery.toLowerCase().match(/\bproject.*niyo\b/i) ||
            userQuery.toLowerCase().match(/\bmga.*project\b/i) ||
            userQuery.toLowerCase().match(/\bmga.*proyekto\b/i) ||
            userQuery.toLowerCase().match(/\bmay.*project\b/i)
        ))) {
      
      setMessages(prev => [...prev, { 
        content: "Our projects portfolio showcases our expertise in construction. We have completed various residential, commercial, and renovation projects with different scope and complexity. Each project demonstrates our commitment to quality and excellence in construction.", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "View Projects", action: "Show me your projects", path: "/projects" },
          { text: "Get Quote", action: "I want a quote for a similar project", path: "/contact" }
        ]
      }]);
      
      setIsLoading(false);
      return;
    }

    // SERVICE-RELATED QUERIES - Provide info without auto-redirect
    if ((userQuery === "What are your services?" || 
        userQuery === "View all services") ||
        (userQuery.length < 50 && ( // Only apply pattern matching on short messages
            userQuery.toLowerCase().match(/\bservice\b/i) ||
            userQuery.toLowerCase().match(/\boffer\b/i) ||
            userQuery.toLowerCase().match(/\bprovide\b/i) ||
            userQuery.toLowerCase().match(/\bwhat.*do.*you.*do\b/i) ||
            userQuery.toLowerCase().match(/\bwhat.*you.*offer\b/i) ||
            userQuery.toLowerCase().match(/\bavailable.*service\b/i) ||
            userQuery.toLowerCase().match(/\bserbisyo\b/i) ||
            userQuery.toLowerCase().match(/\bmga.*serbisyo\b/i) ||
            userQuery.toLowerCase().match(/\boffer.*niyo\b/i) ||
            userQuery.toLowerCase().match(/\bano.*service\b/i) ||
            userQuery.toLowerCase().match(/\banong.*service\b/i) ||
            userQuery.toLowerCase().match(/\bservice.*niyo\b/i) ||
            userQuery.toLowerCase().match(/\bano.*serbisyo\b/i) ||
            userQuery.toLowerCase().match(/\banong.*serbisyo\b/i)
        ))) {
      
      setMessages(prev => [...prev, { 
        content: "We offer a comprehensive range of construction services including commercial construction, residential building, renovation and remodeling, specialized technical services like carpentry and electrical work, and full construction management. Our team can help with all aspects of your construction project.", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "View Services", action: "What are your services?", path: "/services" },
          { text: "Get Quote", action: "I need a quote", path: "/contact" }
        ]
      }]);
      
      setIsLoading(false);
      return;
    }

    // QUOTE/CONTACT-RELATED QUERIES - Provide info without auto-redirect
    if ((userQuery === "I need a quote" || 
        userQuery === "Get Quote") ||
        (userQuery.length < 50 && ( // Only apply pattern matching on short messages
            userQuery.toLowerCase().match(/\bquote\b/i) ||
            userQuery.toLowerCase().match(/\bpricing\b/i) ||
            userQuery.toLowerCase().match(/\bestimate\b/i) ||
            userQuery.toLowerCase().match(/\bcost\b/i) ||
            userQuery.toLowerCase().match(/\bprice\b/i) ||
            userQuery.toLowerCase().match(/\bcontact\b/i) ||
            userQuery.toLowerCase().match(/\btalk.*to.*someone\b/i) ||
            userQuery.toLowerCase().match(/\bspeak.*with\b/i) ||
            userQuery.toLowerCase().match(/\bget.*in.*touch\b/i) ||
            userQuery.toLowerCase().match(/\bneed.*quote\b/i) ||
            userQuery.toLowerCase().match(/\bwant.*quote\b/i) ||
            userQuery.toLowerCase().match(/\bhow.*much\b/i) ||
            userQuery.toLowerCase().match(/\bmagkano\b/i) ||
            userQuery.toLowerCase().match(/\bpresyo\b/i) ||
            userQuery.toLowerCase().match(/\bhalaga\b/i) ||
            userQuery.toLowerCase().match(/\btawag\b/i) ||
            userQuery.toLowerCase().match(/\bcontact\b/i) ||
            userQuery.toLowerCase().match(/\bmakipag-usap\b/i) ||
            userQuery.toLowerCase().match(/\bmakausap\b/i) ||
            userQuery.toLowerCase().match(/\bmakipag.*ugnayan\b/i)
        ))) {
      
      setMessages(prev => [...prev, { 
        content: "You can request a quote by providing details about your construction project. We offer customized quotes based on your specific requirements. You can reach us via phone at 09670598903/09951858305, office line ************, or <NAME_EMAIL>.", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "Contact Us", action: "I want to submit my details", path: "/contact" },
          { text: "View Services", action: "What are your services?", path: "/services" }
        ]
      }]);
      
      setIsLoading(false);
      return;
    }

    // Check if this is a calculation request
    if (userQuery.includes('sqm') && 
        (userQuery.includes('cost') || userQuery.includes('price') || userQuery.includes('estimate')) &&
        (userQuery.includes('residential') || userQuery.includes('commercial') || userQuery.includes('renovation'))) {
      
    const calculationResponse = processCalculation(userQuery);
    if (calculationResponse) {
        setMessages(prev => [...prev, { 
          content: calculationResponse.content, 
          isBot: true, 
          timestamp: new Date(),
          buttons: calculationResponse.buttons
        }]);
        setIsLoading(false);
      return;
      }
    }

    try {
      // Call API and get response
      const response = await fetchChatResponse(userQuery, messages, conversationContext, apiKey);
      
      setMessages(prev => [...prev, { 
        content: response.botResponse, 
        isBot: true, 
        timestamp: new Date(),
        buttons: response.smartButtons,
        formattedContent: response.formattedContent,
        sections: response.sections
      }]);
    } catch (error) {
      console.error('Error in chat response:', error instanceof Error ? error.message : 'Unknown error');
      setMessages(prev => [...prev, { 
        content: "Sorry, may problem po. Please contact us at 09670598903 or <EMAIL>", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "Contact Us", action: "Go to contact page", path: "/contact" }
        ]
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleContactFormSubmit = async (formData: {
    name: string;
    email: string;
    phone: string;
    message: string;
    csrfToken: string;
  }): Promise<boolean> => {
    try {
      // Show loading message
      setMessages(prev => [...prev, { 
        content: "Submitting your contact information...", 
        isBot: true, 
        timestamp: new Date()
      }]);
      
      // Call the contact form submission function
      const success = await submitContactForm(formData);
      
      if (success) {
        // Show success message
        setMessages(prev => [...prev, { 
          content: `Thank you, ${formData.name}! Your information has been submitted successfully. Our team will get back to you soon at ${formData.email}${formData.phone ? ' or ' + formData.phone : ''}.`, 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Continue Chatting", action: "Continue chatting" }
          ]
        }]);
        
        // Save the contact info to the conversation context for future reference
        setConversationContext(prev => ({
          ...prev,
          contactInfo: {
            name: formData.name,
            email: formData.email,
            phone: formData.phone || ''
          },
          hasSubmittedContact: true
        }));
        
        return true;
      } else {
        // Show error message
        setMessages(prev => [...prev, { 
          content: "Sorry, there was an error submitting your information. Please try again later or use our regular contact form on the Contact page.", 
          isBot: true, 
          timestamp: new Date(),
          buttons: [
            { text: "Try Again", action: "quick_contact_form" },
            { text: "Go to Contact Page", action: "Go to Contact Page", path: "/contact" }
          ]
        }]);
        return false;
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      
      // Show error message
      setMessages(prev => [...prev, { 
        content: "Sorry, there was an error submitting your information. Please try again later or use our regular contact form on the Contact page.", 
        isBot: true, 
        timestamp: new Date(),
        buttons: [
          { text: "Try Again", action: "quick_contact_form" },
          { text: "Go to Contact Page", action: "Go to Contact Page", path: "/contact" }
        ]
      }]);
      
      return false;
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    const userMessage = { content: message, isBot: false, timestamp: new Date() };
    setMessages(prev => [...prev, userMessage]);
    setMessage('');

    await handleUserQuery(message);
  };

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Auto-focus input after messages update (when bot responds)
    if (isOpen && !isMinimized && messages.length > 0 && messages[messages.length - 1].isBot) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [messages, isLoading, isOpen, isMinimized]);

  return {
    isOpen,
    isMinimized,
    isExpanded,
    message,
    messages,
    isLoading,
    showHistory,
    savedConversations,
    currentConversationId,
    messagesEndRef,
    chatBoxRef,
    inputRef,
    toggleChat,
    toggleExpand,
    closeChat,
    toggleHistory,
    handleButtonClick,
    handleContactFormSubmit,
    sendMessage,
    setMessage,
    saveCurrentConversation,
    loadConversation,
    startNewConversation,
    deleteConversation
  };
}; 