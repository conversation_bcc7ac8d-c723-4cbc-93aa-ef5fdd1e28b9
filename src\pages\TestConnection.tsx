import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

const TestConnection = () => {
  const [status, setStatus] = useState('Testing...');
  const [details, setDetails] = useState<any>(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      console.log('Testing Supabase connection...');
      
      // Test 1: Basic connection
      const { data, error } = await supabase
        .from('projects')
        .select('count')
        .limit(1);

      if (error) {
        setStatus('Connection Failed');
        setDetails({
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        return;
      }

      // Test 2: Try to get actual data
      const { data: projects, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .limit(5);

      if (projectError) {
        setStatus('Query Failed');
        setDetails({
          error: projectError.message,
          code: projectError.code,
          details: projectError.details
        });
        return;
      }

      setStatus('Connection Successful');
      setDetails({
        projectCount: projects?.length || 0,
        projects: projects,
        supabaseUrl: supabase.supabaseUrl,
        supabaseKey: supabase.supabaseKey ? 'Present' : 'Missing'
      });

    } catch (err) {
      setStatus('Unexpected Error');
      setDetails({
        error: err.message,
        stack: err.stack
      });
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Supabase Connection Test</h1>
      <h2>Status: {status}</h2>
      
      {details && (
        <div>
          <h3>Details:</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '5px',
            overflow: 'auto',
            maxHeight: '400px'
          }}>
            {JSON.stringify(details, null, 2)}
          </pre>
        </div>
      )}
      
      <button onClick={testConnection} style={{ 
        marginTop: '10px', 
        padding: '10px 20px',
        background: '#0066cc',
        color: 'white',
        border: 'none',
        borderRadius: '5px',
        cursor: 'pointer'
      }}>
        Test Again
      </button>
    </div>
  );
};

export default TestConnection;
