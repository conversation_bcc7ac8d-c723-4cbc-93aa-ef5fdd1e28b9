import { supabase } from './supabase';
import type {
  Project, ProjectInsert, ProjectUpdate,
  Service, ServiceInsert, ServiceUpdate,
  CompanyInfo, CompanyInfoInsert, CompanyInfoUpdate,
  BlogPost, BlogPostInsert, BlogPostUpdate,
  MediaFile, MediaFileInsert, MediaFileUpdate,
  AdminUser
} from './database.types';

// Error handling utility
export class DatabaseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'DatabaseError';

    // Enhanced error logging
    console.error('Database Error Details:', {
      message,
      originalError: originalError?.message || originalError,
      code: originalError?.code,
      details: originalError?.details,
      hint: originalError?.hint,
      stack: this.stack
    });
  }
}

// Generic database operations
export const db = {
  // Projects
  projects: {
    // Get all projects (published only for public, all for admin)
    getAll: async (includeUnpublished = false) => {
      const query = supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      // Note: published filtering temporarily disabled until column is added to database
      // if (!includeUnpublished) {
      //   query = query.eq('published', true);
      // }

      const { data, error } = await query;
      if (error) throw new DatabaseError('Failed to fetch projects', error);
      return data as Project[];
    },

    // Get project by ID
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new DatabaseError('Failed to fetch project', error);
      return data as Project;
    },

    // Create new project
    create: async (project: ProjectInsert) => {
      const { data, error } = await supabase
        .from('projects')
        .insert(project)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to create project', error);
      return data as Project;
    },

    // Update project
    update: async (id: string, updates: ProjectUpdate) => {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to update project', error);
      return data as Project;
    },

    // Delete project
    delete: async (id: string) => {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id);

      if (error) throw new DatabaseError('Failed to delete project', error);
    },

    // Get projects by category
    getByCategory: async (category: string, includeUnpublished = false) => {
      let query = supabase
        .from('projects')
        .select('*')
        .eq('category', category)
        .order('created_at', { ascending: false });

      if (!includeUnpublished) {
        query = query.eq('published', true);
      }

      const { data, error } = await query;
      if (error) throw new DatabaseError('Failed to fetch projects by category', error);
      return data as Project[];
    },
  },

  // Services
  services: {
    // Get all services
    getAll: async (includeUnpublished = false) => {
      let query = supabase
        .from('services')
        .select('*')
        .order('created_at', { ascending: false });

      // Temporarily removed published filtering until column is added
      // if (!includeUnpublished) {
      //   query = query.eq('published', true);
      // }

      const { data, error } = await query;
      if (error) throw new DatabaseError('Failed to fetch services', error);
      return data as Service[];
    },

    // Get service by ID
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new DatabaseError('Failed to fetch service', error);
      return data as Service;
    },

    // Create new service
    create: async (service: ServiceInsert) => {
      const { data, error } = await supabase
        .from('services')
        .insert(service)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to create service', error);
      return data as Service;
    },

    // Update service
    update: async (id: string, updates: ServiceUpdate) => {
      const { data, error } = await supabase
        .from('services')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to update service', error);
      return data as Service;
    },

    // Delete service
    delete: async (id: string) => {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) throw new DatabaseError('Failed to delete service', error);
    },

    // Get services by category
    getByCategory: async (category: string, includeUnpublished = false) => {
      let query = supabase
        .from('services')
        .select('*')
        .eq('category', category)
        .order('created_at', { ascending: false });

      if (!includeUnpublished) {
        query = query.eq('published', true);
      }

      const { data, error } = await query;
      if (error) throw new DatabaseError('Failed to fetch services by category', error);
      return data as Service[];
    },
  },

  // Company Information
  companyInfo: {
    // Get all company info
    getAll: async () => {
      const { data, error } = await supabase
        .from('company_info')
        .select('*')
        .order('field_name');

      if (error) throw new DatabaseError('Failed to fetch company info', error);
      return data as CompanyInfo[];
    },

    // Get company info by field name
    getByField: async (fieldName: string) => {
      const { data, error } = await supabase
        .from('company_info')
        .select('*')
        .eq('field_name', fieldName)
        .single();

      if (error) throw new DatabaseError('Failed to fetch company info field', error);
      return data as CompanyInfo;
    },

    // Update or create company info field
    upsert: async (info: CompanyInfoInsert) => {
      const { data, error } = await supabase
        .from('company_info')
        .upsert(info, { onConflict: 'field_name' })
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to update company info', error);
      return data as CompanyInfo;
    },

    // Delete company info field
    delete: async (fieldName: string) => {
      const { error } = await supabase
        .from('company_info')
        .delete()
        .eq('field_name', fieldName);

      if (error) throw new DatabaseError('Failed to delete company info field', error);
    },
  },

  // Blog Posts
  blogPosts: {
    // Get all blog posts
    getAll: async (includeUnpublished = false) => {
      let query = supabase
        .from('blog_posts')
        .select('*')
        .order('published_at', { ascending: false, nullsFirst: false });

      // Temporarily removed published filtering until column is added
      // if (!includeUnpublished) {
      //   query = query.eq('published', true);
      // }

      const { data, error } = await query;
      if (error) throw new DatabaseError('Failed to fetch blog posts', error);
      return data as BlogPost[];
    },

    // Get blog post by ID
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new DatabaseError('Failed to fetch blog post', error);
      return data as BlogPost;
    },

    // Get blog post by slug
    getBySlug: async (slug: string) => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) throw new DatabaseError('Failed to fetch blog post', error);
      return data as BlogPost;
    },

    // Create new blog post
    create: async (post: BlogPostInsert) => {
      const { data, error } = await supabase
        .from('blog_posts')
        .insert(post)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to create blog post', error);
      return data as BlogPost;
    },

    // Update blog post
    update: async (id: string, updates: BlogPostUpdate) => {
      const { data, error } = await supabase
        .from('blog_posts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to update blog post', error);
      return data as BlogPost;
    },

    // Delete blog post
    delete: async (id: string) => {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) throw new DatabaseError('Failed to delete blog post', error);
    },
  },

  // Admin Users
  adminUsers: {
    // Get admin user by ID
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new DatabaseError('Failed to fetch admin user', error);
      return data as AdminUser;
    },

    // Get admin user by email
    getByEmail: async (email: string) => {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('email', email)
        .single();

      if (error) throw new DatabaseError('Failed to fetch admin user by email', error);
      return data as AdminUser;
    },

    // Update last login
    updateLastLogin: async (id: string) => {
      const { data, error } = await supabase
        .from('admin_users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to update last login', error);
      return data as AdminUser;
    },
  },

  // Media Files
  mediaFiles: {
    // Get all media files
    getAll: async () => {
      const { data, error } = await supabase
        .from('media_files')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw new DatabaseError('Failed to fetch media files', error);
      return data as MediaFile[];
    },

    // Get media file by ID
    getById: async (id: string) => {
      const { data, error } = await supabase
        .from('media_files')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new DatabaseError('Failed to fetch media file', error);
      return data as MediaFile;
    },

    // Create new media file record
    create: async (file: MediaFileInsert) => {
      const { data, error } = await supabase
        .from('media_files')
        .insert(file)
        .select()
        .single();

      if (error) throw new DatabaseError('Failed to create media file record', error);
      return data as MediaFile;
    },

    // Delete media file record
    delete: async (id: string) => {
      const { error } = await supabase
        .from('media_files')
        .delete()
        .eq('id', id);

      if (error) throw new DatabaseError('Failed to delete media file record', error);
    },
  },
};
