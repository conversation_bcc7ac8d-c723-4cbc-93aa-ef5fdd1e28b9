import emailjs from '@emailjs/browser';
import { validateCsrfToken, refreshCsrfToken } from './csrf-protection';

// Define the structure of contact form data
export interface ContactFormData {
  from_name: string;
  from_email: string;
  message: string;
  phone?: string;
  subject?: string;
  csrfToken?: string; // Add CSRF token field
  recaptchaToken?: string; // Add reCAPTCHA token field
}

// Initialize EmailJS with the public key
const initializeEmailJS = () => {
  const publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY;
  
  if (!publicKey) {
    console.error('EmailJS public key is missing from environment variables');
    return false;
  }
  
  try {
    emailjs.init(publicKey);
    return true;
  } catch (error) {
    console.error('Failed to initialize EmailJS:', error);
    return false;
  }
};

// Security utilities - Export validateEmail so it can be used directly
export const validateEmail = (email: string): boolean => {
  // Step 1: Basic format validation with stricter rules
  const strictRegex = /^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]{0,63}[a-zA-Z0-9])?@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,62}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,63}$/;
  if (!strictRegex.test(email)) {
    return false;
  }

  // Step 2: Check for common disposable/temporary email domains
  const disposableDomains = [
    'tempmail', 'throwawaymail', 'yopmail', 'mailinator', 'guerrillamail', 
    'temp-mail', 'fakeinbox', 'tempinbox', 'sharklasers', 'guerrilla-mail',
    'jetable', 'nada', 'tempr', 'trash-mail', 'trashmail', 'wegwerfmail', 
    'dispostable', '10minutemail', 'spamgourmet', 'getairmail', 'inboxalias',
    'temporarymail', 'getnada', 'emailondeck', 'emailtemporal'
  ];
  
  const domain = email.split('@')[1].toLowerCase();
  const domainWithoutTLD = domain.substring(0, domain.lastIndexOf('.'));
  
  if (disposableDomains.some(bad => domainWithoutTLD.includes(bad))) {
    return false;
  }

  // Step 3: Only allow specific TLDs
  const validTLDs = [
    'com', 'net', 'org', 'edu', 'gov', 'mil', 'co', 'io', 'ai', 'app',
    'dev', 'info', 'biz', 'name', 'pro', 'ph', 'uk', 'us', 'ca', 'au', 
    'de', 'fr', 'jp', 'ru', 'cn', 'in', 'br', 'it', 'nl', 'es', 'xyz'
  ];
  
  const tld = domain.split('.').pop()?.toLowerCase() || '';
  if (!validTLDs.includes(tld)) {
    return false;
  }

  // Step 4: Additional specific checks for common domains
  if (domain.includes('gmail.com')) {
    // Gmail-specific checks
    const localPart = email.split('@')[0].toLowerCase();
    // Gmail doesn't allow addresses with periods shorter than 6 chars
    if (localPart.replace(/\./g, '').length < 5) {
      return false;
    }
  }

  return true;
};

const sanitizeInput = (input: string): string => {
  // Basic sanitization to prevent XSS
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

// Helper function to verify reCAPTCHA token with Google's API through our server endpoint
const verifyRecaptcha = async (token: string): Promise<boolean> => {
  try {
    if (!token || token.length < 10) {
      return false;
    }

    // Call our API endpoint instead of Google's directly
    const response = await fetch('/api/verify-recaptcha', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      throw new Error(`Verification failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    
    // For now, if we're in development, allow the form to proceed
    if (import.meta.env.DEV) {
      console.log('Development mode: bypassing reCAPTCHA verification');
      return true;
    }
    
    return false;
  }
};

// Send email with error handling and security measures
export const sendContactEmail = async (formData: ContactFormData): Promise<{ success: boolean; message: string }> => {
  try {
    // Validate CSRF token first
    if (!formData.csrfToken || !validateCsrfToken(formData.csrfToken)) {
      throw new Error('Invalid security token. Please reload the page and try again.');
    }

    // Validate reCAPTCHA token
    if (!formData.recaptchaToken) {
      throw new Error('reCAPTCHA verification is required.');
    }

    // Verify reCAPTCHA token
    const isRecaptchaValid = await verifyRecaptcha(formData.recaptchaToken);
    if (!isRecaptchaValid) {
      throw new Error('reCAPTCHA verification failed. Please try again.');
    }

    // Initialize EmailJS
    if (!initializeEmailJS()) {
      throw new Error('Failed to initialize email service');
    }
    
    // Get service and template IDs from environment variables
    const serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID;
    const templateId = import.meta.env.VITE_EMAILJS_TEMPLATE_ID;
    
    if (!serviceId || !templateId) {
      throw new Error('Email service configuration is incomplete');
    }
    
    // Validate email to prevent spoofing
    if (!validateEmail(formData.from_email)) {
      throw new Error('Invalid email address provided');
    }
    
    // Sanitize inputs to prevent XSS
    const sanitizedData = {
      from_name: sanitizeInput(formData.from_name),
      from_email: sanitizeInput(formData.from_email),
      message: sanitizeInput(formData.message),
      phone: formData.phone ? sanitizeInput(formData.phone) : '',
      subject: formData.subject ? sanitizeInput(formData.subject) : `New Inquiry from ${sanitizeInput(formData.from_name)}`
    };
    
    // Send email using EmailJS
    try {
      const response = await emailjs.send(
        serviceId,
        templateId,
        sanitizedData
      );
      
      if (response.status === 200) {
        // Refresh CSRF token after successful submission to prevent token reuse
        refreshCsrfToken();
        
        return { 
          success: true, 
          message: 'Your message has been sent successfully! We will get back to you soon.' 
        };
      } else {
        throw new Error('Failed to send email');
      }
    } catch (emailError: any) {
      // Handle EmailJS specific error format
      if (emailError?.status === 400 && emailError?.text) {
        throw new Error(`EmailJS error: ${emailError.text}`);
      }
      throw emailError; // Re-throw if not a specific EmailJS error
    }
  } catch (error) {
    if (error instanceof Error) {
      return { 
        success: false, 
        message: error.message || 'There was a problem sending your message. Please try again later.'
      };
    }
    return { 
      success: false, 
      message: 'There was a problem sending your message. Please try again later.'
    };
  }
}; 