import { supabase } from './supabase';

// Session management utilities
export class SessionManager {
  private static instance: SessionManager;
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry
  private readonly SESSION_CHECK_INTERVAL = 60 * 1000; // Check every minute
  private readonly MAX_IDLE_TIME = 30 * 60 * 1000; // 30 minutes of inactivity
  
  private lastActivity: number = Date.now();
  private isActive: boolean = true;

  private constructor() {
    this.setupActivityTracking();
    this.startSessionMonitoring();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // Set up activity tracking
  private setupActivityTracking() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const updateActivity = () => {
      this.lastActivity = Date.now();
      if (!this.isActive) {
        this.isActive = true;
        console.log('User activity resumed');
      }
    };

    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.updateActivity();
      }
    });
  }

  // Update last activity timestamp
  private updateActivity() {
    this.lastActivity = Date.now();
  }

  // Check if user has been idle too long
  private isUserIdle(): boolean {
    return Date.now() - this.lastActivity > this.MAX_IDLE_TIME;
  }

  // Start monitoring session
  private startSessionMonitoring() {
    this.refreshTimer = setInterval(async () => {
      await this.checkAndRefreshSession();
    }, this.SESSION_CHECK_INTERVAL);
  }

  // Stop monitoring session
  public stopSessionMonitoring() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // Check session and refresh if needed
  private async checkAndRefreshSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error checking session:', error);
        return;
      }

      if (!session) {
        // No session, user is logged out
        this.handleSessionExpired();
        return;
      }

      // Check if user has been idle too long
      if (this.isUserIdle()) {
        console.log('User has been idle too long, logging out');
        await this.forceLogout('Session expired due to inactivity');
        return;
      }

      // Check if session needs refresh
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const timeUntilExpiry = expiresAt - Date.now();

      if (timeUntilExpiry <= this.REFRESH_THRESHOLD) {
        console.log('Session expiring soon, attempting refresh');
        await this.refreshSession();
      }
    } catch (error) {
      console.error('Error in session monitoring:', error);
    }
  }

  // Refresh the current session
  private async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error('Error refreshing session:', error);
        await this.handleSessionExpired();
        return;
      }

      if (data.session) {
        console.log('Session refreshed successfully');
        this.updateActivity(); // Reset activity timer on successful refresh
      } else {
        console.log('No session returned from refresh');
        await this.handleSessionExpired();
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
      await this.handleSessionExpired();
    }
  }

  // Handle session expiration
  private async handleSessionExpired() {
    console.log('Session expired, redirecting to login');
    this.stopSessionMonitoring();
    
    // Clear any stored session data
    await this.clearSessionData();
    
    // Redirect to login page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/admin/login')) {
      window.location.href = `/admin/login?from=${encodeURIComponent(currentPath)}`;
    }
  }

  // Force logout with message
  public async forceLogout(reason?: string) {
    console.log('Force logout:', reason);
    
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Error during force logout:', error);
    }
    
    await this.clearSessionData();
    this.stopSessionMonitoring();
    
    // Show logout reason if provided
    if (reason) {
      sessionStorage.setItem('logout_reason', reason);
    }
    
    window.location.href = '/admin/login';
  }

  // Clear session-related data
  private async clearSessionData() {
    try {
      // Clear any cached data
      sessionStorage.removeItem('rcs_csrf_token');
      
      // Clear any other session-specific data
      // Add more cleanup as needed
    } catch (error) {
      console.error('Error clearing session data:', error);
    }
  }

  // Manual session refresh (called by user action)
  public async manualRefresh(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error || !data.session) {
        return false;
      }
      
      this.updateActivity();
      return true;
    } catch (error) {
      console.error('Error in manual refresh:', error);
      return false;
    }
  }

  // Get session info
  public async getSessionInfo() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        return null;
      }

      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const timeUntilExpiry = expiresAt - Date.now();
      const idleTime = Date.now() - this.lastActivity;

      return {
        isValid: timeUntilExpiry > 0,
        expiresAt: new Date(expiresAt),
        timeUntilExpiry,
        idleTime,
        isIdle: this.isUserIdle(),
        user: session.user,
      };
    } catch (error) {
      console.error('Error getting session info:', error);
      return null;
    }
  }

  // Initialize session management for admin area
  public static initializeForAdmin() {
    const manager = SessionManager.getInstance();
    
    // Add warning before session expires
    const showExpiryWarning = () => {
      // You can implement a toast notification here
      console.warn('Your session will expire soon. Please save your work.');
    };

    // Check if we're in admin area
    if (window.location.pathname.startsWith('/admin')) {
      // Set up expiry warning
      setTimeout(showExpiryWarning, manager.REFRESH_THRESHOLD - 60000); // 1 minute before refresh threshold
    }

    return manager;
  }
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance();

// Utility functions
export const initializeSessionManagement = () => {
  return SessionManager.initializeForAdmin();
};

export const getSessionInfo = () => {
  return sessionManager.getSessionInfo();
};

export const forceLogout = (reason?: string) => {
  return sessionManager.forceLogout(reason);
};

export const refreshSession = () => {
  return sessionManager.manualRefresh();
};
