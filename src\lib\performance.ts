/**
 * Performance utility functions to optimize website speed
 */

// Debounce function to limit how often a function is called
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      window.clearTimeout(timeout);
    }
    timeout = window.setTimeout(() => {
      func(...args);
    }, wait);
  };
}

// Throttle function to limit execution to once per specified interval
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

// Lazy load images 
export function setupLazyLoading(): void {
  // Only run in browser environment - safe check
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return;
  }
  
  // Function to actually load images
  const loadImages = () => {
    // Find all images with data-src attribute
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    if (lazyImages.length === 0) return;
    
    // Use modern approach if supported
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            imageObserver.unobserve(img);
          }
        });
      });
      
      lazyImages.forEach(img => imageObserver.observe(img));
    } else {
      // Fallback approach - just load all images
      lazyImages.forEach(img => {
        const imgElement = img as HTMLImageElement;
        if (imgElement.dataset.src) {
          imgElement.src = imgElement.dataset.src;
          imgElement.removeAttribute('data-src');
        }
      });
    }
  };
  
  // Run once immediately in case images are already in viewport
  loadImages();
  
  // Then set up to run again when DOM is fully loaded
  if (document.readyState === 'complete') {
    loadImages();
  } else {
    // Use a safer approach for event listener
    document.onreadystatechange = function() {
      if (document.readyState === 'complete') {
        loadImages();
      }
    };
  }
}

// Cache DOM elements to avoid frequent querySelector calls
export function cacheDomElements<T extends HTMLElement>(
  selectors: Record<string, string>
): Record<string, T | null> {
  const cache: Record<string, T | null> = {};
  
  for (const key in selectors) {
    cache[key] = document.querySelector(selectors[key]) as T | null;
  }
  
  return cache;
}

// Optimize animations by checking if browser supports requestAnimationFrame
export function optimizedAnimation(callback: FrameRequestCallback): number {
  if (window.requestAnimationFrame) {
    return window.requestAnimationFrame(callback);
  } else {
    // Fallback for older browsers
    return window.setTimeout(callback, 16); // 60fps ≈ 16ms per frame
  }
}

// Cancel optimized animation
export function cancelOptimizedAnimation(id: number): void {
  if (window.cancelAnimationFrame) {
    window.cancelAnimationFrame(id);
  } else {
    window.clearTimeout(id);
  }
}

// Preload critical resources
export function preloadResources(urls: string[]): void {
  if (typeof document === 'undefined') return;
  
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    // Set appropriate as attribute based on file type
    if (url.endsWith('.css')) {
      link.as = 'style';
    } else if (url.endsWith('.js')) {
      link.as = 'script';
    } else if (/\.(png|jpg|jpeg|gif|webp|avif)$/i.test(url)) {
      link.as = 'image';
    } else if (/\.(woff|woff2|ttf|otf)$/i.test(url)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  });
} 