import React from 'react';
import { Button } from '@/components/ui/button';

const AdminBypass = () => {
  const handleBypass = () => {
    // Create a fake admin user for bypass
    const fakeAdmin = {
      id: '7d537368-f51c-4274-a55c-b53b8f190e5f',
      email: '<EMAIL>',
      role: 'admin',
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString()
    };
    
    // Save to localStorage
    localStorage.setItem('admin_user', JSON.stringify(fakeAdmin));
    
    // Reload page to trigger auth
    window.location.reload();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Admin Access
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Temporary admin bypass for testing
          </p>
        </div>
        
        <div className="space-y-4">
          <Button 
            onClick={handleBypass}
            className="w-full"
            size="lg"
          >
            Access Admin Panel
          </Button>
          
          <p className="text-xs text-gray-500 text-center">
            This bypasses authentication for development purposes
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminBypass;
