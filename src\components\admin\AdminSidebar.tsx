import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Building2, 
  Wrench, 
  Building, 
  FileText, 
  Image, 
  Settings,
  BarChart3,
  Users,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePermissions, Permission } from '@/lib/permissions';
import { PermissionGate } from '@/lib/permissions';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  permission?: Permission;
  badge?: string;
  children?: NavItem[];
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onClose, isMobile }) => {
  const location = useLocation();
  const { hasPermission } = usePermissions();

  // Navigation items with permissions
  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Projects',
      href: '/admin/projects',
      icon: Building2,
      permission: Permission.VIEW_PROJECTS,
      children: [
        {
          name: 'All Projects',
          href: '/admin/projects',
          icon: Building2,
          permission: Permission.VIEW_PROJECTS,
        },
        {
          name: 'Add Project',
          href: '/admin/projects/new',
          icon: Building2,
          permission: Permission.CREATE_PROJECTS,
        },
      ],
    },
    {
      name: 'Services',
      href: '/admin/services',
      icon: Wrench,
      permission: Permission.VIEW_SERVICES,
      children: [
        {
          name: 'All Services',
          href: '/admin/services',
          icon: Wrench,
          permission: Permission.VIEW_SERVICES,
        },
        {
          name: 'Add Service',
          href: '/admin/services/new',
          icon: Wrench,
          permission: Permission.CREATE_SERVICES,
        },
      ],
    },
    {
      name: 'Company Info',
      href: '/admin/company',
      icon: Building,
      permission: Permission.VIEW_COMPANY_INFO,
    },
    {
      name: 'Blog',
      href: '/admin/blog',
      icon: FileText,
      permission: Permission.VIEW_BLOG_POSTS,
      children: [
        {
          name: 'All Posts',
          href: '/admin/blog',
          icon: FileText,
          permission: Permission.VIEW_BLOG_POSTS,
        },
        {
          name: 'New Post',
          href: '/admin/blog/new',
          icon: FileText,
          permission: Permission.CREATE_BLOG_POSTS,
        },
      ],
    },
    {
      name: 'Media',
      href: '/admin/media',
      icon: Image,
      permission: Permission.VIEW_MEDIA,
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      permission: Permission.VIEW_ANALYTICS,
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: Users,
      permission: Permission.VIEW_USERS,
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      permission: Permission.MANAGE_SETTINGS,
    },
  ];

  // Check if current path matches nav item
  const isActiveRoute = (href: string) => {
    if (href === '/admin/dashboard') {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  // Render navigation item
  const renderNavItem = (item: NavItem, level = 0) => {
    // Check permissions
    if (item.permission && !hasPermission(item.permission)) {
      return null;
    }

    const isActive = isActiveRoute(item.href);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.href}>
        <Link
          to={item.href}
          onClick={isMobile ? onClose : undefined}
          className={cn(
            'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
            level > 0 && 'ml-4 pl-8',
            isActive
              ? 'bg-rcs-blue text-white'
              : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
          )}
        >
          <item.icon 
            size={18} 
            className={cn(
              'mr-3 flex-shrink-0',
              isActive ? 'text-white' : 'text-gray-400'
            )} 
          />
          <span className="flex-1">{item.name}</span>
          {item.badge && (
            <span className={cn(
              'ml-2 px-2 py-1 text-xs rounded-full',
              isActive 
                ? 'bg-white text-rcs-blue' 
                : 'bg-gray-200 text-gray-600'
            )}>
              {item.badge}
            </span>
          )}
        </Link>

        {/* Render children if expanded and has children */}
        {hasChildren && isActive && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Sidebar */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out',
        isMobile 
          ? (isOpen ? 'translate-x-0' : '-translate-x-full')
          : 'translate-x-0'
      )}>
        {/* Logo and brand */}
        <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
          <Link to="/admin/dashboard" className="flex items-center space-x-2">
            <img
              src="/rcslogo.webp"
              alt="RCS"
              className="h-8 w-8"
            />
            <div className="flex flex-col">
              <span className="text-lg font-bold text-gray-900">RCS</span>
              <span className="text-xs text-gray-500">Admin Panel</span>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {navigationItems.map(item => renderNavItem(item))}
        </nav>

        {/* Bottom section */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center space-x-3 text-sm text-gray-600">
            <HelpCircle size={16} />
            <span>Need help?</span>
          </div>
          <Link
            to="/admin/help"
            className="mt-2 text-xs text-rcs-blue hover:underline"
          >
            View documentation
          </Link>
        </div>
      </div>
    </>
  );
};

export default AdminSidebar;
