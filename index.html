<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RCS | Rodelas Construction Services</title>
    <meta name="description" content="Rodelas Construction Services - Excellence in construction, renovation, and engineering since 2010. Commercial, residential, and industrial projects." />
    <meta name="author" content="Rodelas Construction Services" />
    
    <link rel="preload" href="/rcslogo.webp" as="image" fetchpriority="high" />
    <link rel="modulepreload" href="/src/main.tsx" crossorigin />
    <link rel="modulepreload" href="/src/App.tsx" />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="dns-prefetch" href="https://api.emailjs.com" />
    
    <link rel="icon" href="/rcslogo.webp" type="image/webp" />
    <link rel="apple-touch-icon" href="/rcslogo.webp" />
    <link rel="manifest" href="/manifest.json" />
    
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'" />
    
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co https://generativelanguage.googleapis.com https://vercel.live/_next-live/feedback/ https://*.google.com https://*.gstatic.com https://*.googleapis.com https://*.vercel.live https://*.emailjs.com; connect-src 'self' https://generativelanguage.googleapis.com https://*.vercel.live https://*.google.com https://*.googleapis.com https://api.emailjs.com https://*.supabase.co; img-src 'self' data: https://*.unsplash.com https://*.google.com https://*.googleapis.com https://*.gstatic.com https://*.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; frame-src 'self' https://*.google.com https://www.google.com/maps/ https://*.vercel.app https://vercel.live/ https://*.vercel.live;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <meta http-equiv="Permissions-Policy" content="geolocation=(), camera=(), microphone=()">
    
    <meta http-equiv="Cache-Control" content="max-age=31536000" />

    <meta property="og:title" content="RCS | Rodelas Construction Services" />
    <meta property="og:description" content="Rodelas Construction Services - Excellence in construction, renovation, and engineering since 2010." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/rcslogo.webp" />
    <meta name="twitter:card" content="summary" />
    <meta name="theme-color" content="#0c4da2" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    
    <style>
      :root {
        --rcs-blue: #0c4da2;
        --rcs-gold: #d4af37;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
        background-color: #f9fafb;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      .initial-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100%;
        background-color: #f9fafb;
      }
      
      .initial-spinner {
        border: 3px solid #e2e8f0;
        border-top: 3px solid var(--rcs-blue);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .off-screen {
        content-visibility: auto;
        contain-intrinsic-size: 0 500px;
      }
    </style>
    
    <link rel="preload" href="/src/index.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/src/index.css"></noscript>
  </head>
  <body>
    <div id="root">
      <div class="initial-loading">
        <div class="initial-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
