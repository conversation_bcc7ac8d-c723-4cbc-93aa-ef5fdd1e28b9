import React from 'react';

/**
 * Utility to convert JPG paths to WebP with fallback support
 * 
 * @param src - The original image path (JPG)
 * @returns - The WebP path with the same filename
 */
export const getOptimizedImagePath = (src: string): string => {
  if (!src) return '';
  
  // For external URLs, return as is
  if (src.startsWith('http')) {
    return src;
  }
  
  // Replace .jpg extension with .webp
  return src.replace(/\.jpg$/i, '.webp');
};

/**
 * Creates a picture element with WebP and JPG sources
 * This helps with browser compatibility
 * 
 * @param props - Standard img props
 * @returns - JSX for picture element
 */
export const OptimizedImage: React.FC<React.ImgHTMLAttributes<HTMLImageElement>> = ({ 
  src, 
  alt, 
  className = '', 
  width,
  height,
  loading = 'lazy',
  ...rest 
}) => {
  if (!src) {
    return React.createElement('img', {
      src: '/images/fallback-image.webp',
      alt: alt || 'Image not found',
      className,
      ...rest
    });
  }
  
  const webpSrc = getOptimizedImagePath(src);
  
  return React.createElement('picture', {}, [
    React.createElement('source', { 
      key: 'webp-source',
      srcSet: webpSrc, 
      type: 'image/webp' 
    }),
    React.createElement('source', { 
      key: 'jpeg-source',
      srcSet: src, 
      type: 'image/jpeg' 
    }),
    React.createElement('img', {
      key: 'main-image',
      src,
      alt: alt || 'Rodelas Construction Services',
      className,
      width,
      height,
      loading,
      ...rest
    })
  ]);
};
