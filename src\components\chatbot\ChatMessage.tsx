import React, { useState, useMemo } from 'react';
import { MessageSquare, CheckCircle, ArrowRight, FileText, Info, Book, List, HelpCircle, Wrench, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/button';
import { Message } from './types';
import { Phone, Mail, MapPin, Info as InfoIcon, Calculator } from 'lucide-react';

interface ChatMessageProps {
  message: Message;
  onButtonClick: (action: string, path?: string) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, onButtonClick }) => {
  const [isSubmittingForm, setIsSubmittingForm] = useState(false);
  
  // Function to format message content with email handling
  const formatContentWithEmailHandling = (content: string) => {
    // Regex to identify email addresses
    const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/g;
    
    // Replace email addresses with a span that has word-break: break-all
    return content.replace(emailRegex, (match) => 
      `<span style="word-break: break-all; display: inline-block;">${match}</span>`
    );
  };
  
  // Process and format the content if needed
  const formattedContent = useMemo(() => {
    if (!message.isBot || !message.formattedContent) return null;
    
    // Format the content by replacing asterisks and other markdown-like syntax
    let content = message.content;
    
    // Replace section headers (** Title: **)
    content = content.replace(/\*\*(.*?):\*\*/g, '<div class="text-rcs-blue font-semibold text-base mt-3 mb-1">$1</div>');
    
    // Replace asterisks for emphasis (* point *)
    content = content.replace(/\* (.*?)(?:\n|$)/g, '<div class="flex items-start mt-1.5"><span class="text-rcs-blue mr-2 mt-1">•</span><span>$1</span></div>');
    
    // Replace double asterisks for bold (**text**)
    content = content.replace(/\*\*(.*?)\*\*/g, '<span class="font-medium">$1</span>');
    
    // Handle email addresses
    content = formatContentWithEmailHandling(content);
    
    return { __html: content };
  }, [message.content, message.isBot, message.formattedContent]);
  
  // Extract sections from content if they exist in a specific format
  const processedSections = useMemo(() => {
    if (!message.isBot || message.sections) return null;
    
    // Look for patterns like "**Section Title:** content"
    const sectionRegex = /\*\*(.*?):\*\*([\s\S]*?)(?=\*\*[\w\s]+:\*\*|$)/g;
    let match;
    const sections = [];
    
    let content = message.content;
    while ((match = sectionRegex.exec(content)) !== null) {
      if (match[1] && match[2]) {
        sections.push({
          title: match[1].trim(),
          content: match[2].trim()
        });
      }
    }
    
    return sections.length > 0 ? sections : null;
  }, [message.content, message.isBot, message.sections]);
  
  // Get icon for section
  const getSectionIcon = (title: string) => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('material')) return <FileText size={16} className="text-blue-500" />;
    if (lowerTitle.includes('phase') || lowerTitle.includes('step')) return <ArrowRight size={16} className="text-green-500" />;
    if (lowerTitle.includes('method') || lowerTitle.includes('tool')) return <Wrench size={16} className="text-orange-500" />;
    if (lowerTitle.includes('safety')) return <AlertTriangle size={16} className="text-red-500" />;
    if (lowerTitle.includes('challenge')) return <AlertTriangle size={16} className="text-amber-500" />;
    if (lowerTitle.includes('cost') || lowerTitle.includes('budget')) return <Calculator size={16} className="text-purple-500" />;
    if (lowerTitle.includes('management')) return <List size={16} className="text-indigo-500" />;
    if (lowerTitle.includes('skill') || lowerTitle.includes('trade')) return <CheckCircle size={16} className="text-teal-500" />;
    if (lowerTitle.includes('career') || lowerTitle.includes('development')) return <Book size={16} className="text-blue-600" />;
    return <Info size={16} className="text-gray-500" />;
  };
  
  return (
    <div 
      className={`mb-3 md:mb-4 flex ${message.isBot ? 'justify-start' : 'justify-end'} items-end animate-fadeIn`}
    >
      {message.isBot && (
        <div className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-gradient-to-r from-rcs-blue to-blue-700 flex items-center justify-center mr-2 mb-0.5 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
          <MessageSquare className="h-3.5 w-3.5 md:h-4 md:w-4 text-white" />
        </div>
      )}
      
      <div 
        className={`max-w-[80%] md:max-w-[85%] rounded-xl p-3 md:p-4 shadow-md transition-all duration-300 ${
          message.isBot
            ? 'bg-white text-gray-800 border border-gray-100 hover:shadow-lg hover:border-blue-100'
            : 'bg-gradient-to-r from-rcs-blue to-blue-700 text-white'
        }`}
        style={{
          animationDelay: '0.2s',
          animationDuration: '0.5s'
        }}
      >
        {/* Render content based on type */}
        {message.isBot && message.sections ? (
          // Render structured sections
          <div className="space-y-3">
            {message.sections.map((section, index) => (
              <div key={index} className="animate-fadeIn" style={{ animationDelay: `${0.1 * (index + 1)}s` }}>
                <div className="flex items-center gap-1.5 text-rcs-blue font-semibold pb-1 border-b border-gray-100">
                  {section.icon ? section.icon : getSectionIcon(section.title)}
                  <span>{section.title}</span>
                </div>
                <p className="mt-1.5 text-sm md:text-base leading-relaxed">{section.content}</p>
              </div>
            ))}
          </div>
        ) : message.isBot && processedSections ? (
          // Render auto-detected sections
          <div className="space-y-3">
            {processedSections.map((section, index) => (
              <div key={index} className="animate-fadeIn" style={{ animationDelay: `${0.1 * (index + 1)}s` }}>
                <div className="flex items-center gap-1.5 text-rcs-blue font-semibold pb-1 border-b border-gray-100">
                  {getSectionIcon(section.title)}
                  <span>{section.title}</span>
                </div>
                <p className="mt-1.5 text-sm md:text-base leading-relaxed">{section.content}</p>
              </div>
            ))}
          </div>
        ) : message.isBot && formattedContent ? (
          // Render HTML formatted content
          <div 
            className="text-sm md:text-base break-words leading-relaxed"
            dangerouslySetInnerHTML={formattedContent}
          />
        ) : (
          // Render regular content
          <div 
            className="text-sm md:text-base break-words leading-relaxed whitespace-pre-wrap"
            dangerouslySetInnerHTML={{ __html: formatContentWithEmailHandling(message.content) }}
          ></div>
        )}
        
        {/* Action buttons */}
        {message.isBot && message.buttons && message.buttons.length > 0 && (
          <div className="flex flex-wrap gap-1.5 md:gap-2 mt-2 md:mt-3 animate-fadeIn" style={{ animationDelay: '0.3s' }}>
            {message.buttons.map((btn, btnIndex) => (
              <Button
                key={btnIndex}
                size="sm"
                variant="outline"
                className="bg-white text-rcs-blue border-rcs-blue hover:bg-rcs-blue hover:text-white text-xs py-1.5 md:py-2 h-auto min-h-[32px] md:min-h-[36px] px-3 md:px-4 rounded-full transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md"
                onClick={() => onButtonClick(btn.action, btn.path)}
              >
                {btn.text === "Call Now" && <Phone className="h-3 w-3 md:h-3.5 md:w-3.5 mr-1 md:mr-1.5" />}
                {btn.text === "Send Email" && <Mail className="h-3 w-3 md:h-3.5 md:w-3.5 mr-1 md:mr-1.5" />}
                {btn.text === "View Map" && <MapPin className="h-3 w-3 md:h-3.5 md:w-3.5 mr-1 md:mr-1.5" />}
                {btn.text === "Calculate" && <Calculator className="h-3 w-3 md:h-3.5 md:w-3.5 mr-1 md:mr-1.5" />}
                {btn.text === "Services Details" && <InfoIcon className="h-3 w-3 md:h-3.5 md:w-3.5 mr-1 md:mr-1.5" />}
                {btn.text}
              </Button>
            ))}
          </div>
        )}
        
        <p className={`text-xs text-right mt-1 md:mt-2 ${message.isBot ? 'text-gray-400' : 'text-blue-100'}`}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </p>
      </div>
    </div>
  );
};

export default ChatMessage; 