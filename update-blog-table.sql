-- Update blog_posts table to match the new schema
-- Run this in Supabase SQL Editor

-- Add missing columns if they don't exist
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'draft',
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT false;

-- Update existing records to have proper status and published fields
UPDATE blog_posts 
SET published = true 
WHERE published_at IS NOT NULL AND published IS NULL;

UPDATE blog_posts 
SET status = CASE 
  WHEN published_at IS NOT NULL THEN 'published'
  ELSE 'draft'
END
WHERE status IS NULL;

-- Make author field have a default value
ALTER TABLE blog_posts 
ALTER COLUMN author SET DEFAULT 'Admin';

-- Update any null author fields
UPDATE blog_posts 
SET author = 'Admin' 
WHERE author IS NULL;
