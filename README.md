# Rodelas Construction Services (RCS) Website

## Personal Project by <PERSON>

This is a personal project developed by <PERSON>. The Rodelas Construction Services website is a construction company concept I created to showcase my web development skills and technical abilities. This project demonstrates my proficiency in modern web technologies and UI/UX design.

## Project Features

- Responsive design optimized for all device sizes
- Interactive service showcase with detailed information
- Dynamic project portfolio with filtering capabilities
- Functional contact form with validation
- Custom AI chatbot assistant integration
- Interactive cost estimator for quick project quotes

## Technical Implementation

As the developer, I've implemented several security best practices:

1. Content Security Policy (CSP) headers to prevent XSS attacks
2. Environment variables for sensitive API key management
3. Input sanitization against injection attacks
4. Image path validation to prevent path traversal
5. Secure headers protecting against clickjacking and other attacks

## Development Setup

### Prerequisites

- Node.js 16+ and npm

### Installation

1. Clone the repository
   ```
   git clone <repository-url>
   cd rodelascons
   ```

2. Install dependencies
   ```
   npm install
   ```

3. Create environment file
   ```
   cp .env.example .env.local
   ```

4. Edit `.env.local` and add API keys:
   ```
   VITE_GEMINI_API_KEY=your-gemini-api-key
   ```

### Development

Start the development server:
```
npm run dev
```

### Production Build

Build for production:
```
npm run build
```

Serve the production build:
```
npm run serve
```

## Security Documentation

For detailed information about the security features I've implemented in this project, please refer to the [SECURITY.md](./SECURITY.md) file.

## Project Structure

- `/src` - Source code
  - `/components` - Reusable UI components
  - `/pages` - Page components
  - `/hooks` - Custom React hooks
  - `/lib` - Utility functions and services

- `/public` - Static assets
  - `/images` - Images used throughout the site
    - `/projects` - Project images organized by category
    
- `/docs` - Documentation files

## License & Ownership

This is a personal project created and owned by Glenn Mark. All rights reserved. This code is proprietary and confidential.
