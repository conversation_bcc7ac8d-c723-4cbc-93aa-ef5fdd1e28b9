import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAdmin?: boolean;
  fallbackPath?: string;
}

const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-rcs-blue"></div>
  </div>
);

const UnauthorizedMessage: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
    <div className="max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h2>
      <p className="text-gray-600 mb-6">{message}</p>
      <button 
        onClick={() => window.location.href = '/'}
        className="px-6 py-2 bg-rcs-blue text-white rounded-lg hover:bg-rcs-blue/90 transition-colors"
      >
        Return to Home
      </button>
    </div>
  </div>
);

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAdmin = true,
  fallbackPath = '/admin/login'
}) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner />;
  }

  // If not authenticated, redirect to login with return URL
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If admin access is required but user is not admin
  if (requireAdmin && !isAdmin) {
    return (
      <UnauthorizedMessage 
        message="You need administrator privileges to access this page." 
      />
    );
  }

  // If all checks pass, render the protected content
  return <>{children}</>;
};

// Higher-order component for protecting routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAdmin?: boolean; fallbackPath?: string } = {}
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for programmatic route protection
export const useRouteProtection = (requireAdmin = true) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();
  const location = useLocation();

  const checkAccess = () => {
    if (loading) return { allowed: false, loading: true };
    
    if (!isAuthenticated) {
      return { 
        allowed: false, 
        loading: false, 
        redirectTo: `/admin/login?from=${encodeURIComponent(location.pathname)}` 
      };
    }
    
    if (requireAdmin && !isAdmin) {
      return { 
        allowed: false, 
        loading: false, 
        error: 'Admin privileges required' 
      };
    }
    
    return { allowed: true, loading: false };
  };

  return checkAccess();
};

export default ProtectedRoute;
