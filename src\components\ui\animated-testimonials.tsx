"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type Testimonial = {
  quote: string;
  name: string;
  designation: string;
  src: string;
};

export const AnimatedTestimonials = ({
  testimonials,
  autoplay = false,
  className,
  darkMode = false,
}: {
  testimonials: Testimonial[];
  autoplay?: boolean;
  className?: string;
  darkMode?: boolean;
}) => {
  const [active, setActive] = useState(0);

  const handleNext = () => {
    setActive((prev) => (prev + 1) % testimonials.length);
  };

  const handlePrev = () => {
    setActive((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const isActive = (index: number) => {
    return index === active;
  };

  useEffect(() => {
    if (autoplay) {
      const interval = setInterval(handleNext, 5000);
      return () => clearInterval(interval);
    }
  }, [autoplay]);

  const randomRotateY = () => {
    return Math.floor(Math.random() * 21) - 10;
  };

  return (
    <div className={cn("max-w-sm md:max-w-6xl mx-auto px-4 md:px-8 lg:px-12 py-20", className)}>
      <div className={cn(
        "relative grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-16 items-center",
        darkMode ? "text-white" : ""
      )}>
        <div className="order-2 md:order-1">
          <div className="relative h-[420px] w-full">
            <AnimatePresence>
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.src}
                  initial={{
                    opacity: 0,
                    scale: 0.9,
                    z: -100,
                    rotate: randomRotateY(),
                  }}
                  animate={{
                    opacity: isActive(index) ? 1 : 0.7,
                    scale: isActive(index) ? 1 : 0.95,
                    z: isActive(index) ? 0 : -100,
                    rotate: isActive(index) ? 0 : randomRotateY(),
                    zIndex: isActive(index)
                      ? 999
                      : testimonials.length + 2 - index,
                    y: isActive(index) ? [0, -20, 0] : 0,
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.9,
                    z: 100,
                    rotate: randomRotateY(),
                  }}
                  transition={{
                    duration: 0.4,
                    ease: "easeInOut",
                  }}
                  className="absolute inset-0 origin-bottom"
                  style={{
                    perspective: "1000px",
                    transformStyle: "preserve-3d"
                  }}
                >
                  <img
                    src={testimonial.src}
                    alt={testimonial.name}
                    className="h-full w-full rounded-2xl object-cover object-center shadow-xl"
                    draggable={false}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
          
          <div className="flex justify-center mt-6 space-x-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`h-4 rounded-full transition-all duration-300 ${
                  active === index ? `bg-rcs-blue w-8` : darkMode ? 'bg-gray-400' : 'bg-gray-300'
                } min-w-[16px]`}
                onClick={() => setActive(index)}
                aria-label={`View testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
        
        <div className="flex justify-between flex-col py-4 order-1 md:order-2">
          <motion.div
            key={active}
            initial={{
              y: 20,
              opacity: 0,
            }}
            animate={{
              y: 0,
              opacity: 1,
            }}
            exit={{
              y: -20,
              opacity: 0,
            }}
            transition={{
              duration: 0.2,
              ease: "easeInOut",
            }}
            className={cn(
              "bg-white rounded-2xl p-8 shadow-lg",
              darkMode ? "bg-black/80 backdrop-blur-md" : ""
            )}
          >
            <h3 className={cn(
              "text-2xl font-bold",
              darkMode ? "text-white" : "text-rcs-blue"
            )}>
              {testimonials[active].name}
            </h3>
            <p className={cn(
              "text-sm",
              darkMode ? "text-gray-300" : "text-rcs-gold font-medium"
            )}>
              {testimonials[active].designation}
            </p>
            <motion.p className={cn(
              "text-lg mt-6",
              darkMode ? "text-gray-200" : "text-gray-700"
            )}>
              {testimonials[active].quote.split(" ").map((word, index) => (
                <motion.span
                  key={index}
                  initial={{
                    filter: "blur(10px)",
                    opacity: 0,
                    y: 5,
                  }}
                  animate={{
                    filter: "blur(0px)",
                    opacity: 1,
                    y: 0,
                  }}
                  transition={{
                    duration: 0.2,
                    ease: "easeInOut",
                    delay: 0.02 * index,
                  }}
                  className="inline-block"
                >
                  {word}&nbsp;
                </motion.span>
              ))}
            </motion.p>
            
            <div className="flex gap-4 justify-end mt-6">
              <button
                onClick={handlePrev}
                className={cn(
                  "h-10 w-10 rounded-full flex items-center justify-center group hover:bg-opacity-80 transition-colors",
                  darkMode ? "bg-white/10" : "bg-rcs-blue/10"
                )}
                aria-label="Previous testimonial"
              >
                <ChevronLeft className={cn(
                  "h-5 w-5 group-hover:-translate-x-1 transition-transform",
                  darkMode ? "text-white" : "text-rcs-blue"
                )} />
              </button>
              <button
                onClick={handleNext}
                className={cn(
                  "h-10 w-10 rounded-full flex items-center justify-center group hover:bg-opacity-80 transition-colors",
                  darkMode ? "bg-white/10" : "bg-rcs-blue/10"
                )}
                aria-label="Next testimonial"
              >
                <ChevronRight className={cn(
                  "h-5 w-5 group-hover:translate-x-1 transition-transform",
                  darkMode ? "text-white" : "text-rcs-blue"
                )} />
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
