import ChatBot from '../ChatBot';
import Chat<PERSON>eader from './ChatHeader';
import ChatContainer from './ChatContainer';
import ChatInput from './ChatInput';
import ChatToggleButton from './ChatToggleButton';
import MinimizedPreview from './MinimizedPreview';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';
import HistoryPanel from './HistoryPanel';
import { useChat } from './hooks/useChat';
import { Message, ConversationContext } from './types';

export {
  ChatBot,
  ChatHeader,
  ChatContainer,
  ChatInput,
  ChatToggleButton,
  MinimizedPreview,
  ChatMessage,
  TypingIndicator,
  HistoryPanel,
  useChat,
  // Types
  type Message,
  type ConversationContext
};

export default ChatBot; 