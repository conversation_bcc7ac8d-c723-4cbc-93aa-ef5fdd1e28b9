import { Message, ConversationContext } from './types';

interface ApiResponse {
  botResponse: string;
  smartButtons?: Array<{
    text: string;
    action: string;
    path?: string;
  }>;
  formattedContent?: boolean;
  hasMultipleSections?: boolean;
  isQAResponse?: boolean;
  sections?: Array<{
    title: string;
    content: string;
  }>;
}

// Function to handle API requests to the Gemini API
export const fetchChatResponse = async (
  userQuery: string, 
  messages: Message[], 
  conversationContext: ConversationContext,
  apiKey: string
): Promise<ApiResponse> => {
  try {
    // Prepare conversation history for the API - increase context window
    const history = messages.slice(-10).map(msg => ({ // Send last 10 messages for better context
      role: msg.isBot ? "model" : "user",
      parts: [{ text: msg.content }]
    }));

    // Add the current user query to the history
    history.push({
      role: "user",
      parts: [{ text: userQuery }]
    });
    
    // Remove the last user message from history if it's the same as the current query (to avoid duplication if called via button)
    if (history.length > 1 && history[history.length - 2].role === "user" && history[history.length - 2].parts[0].text === userQuery) {
      history.splice(history.length - 2, 1);
    }

    // Prepare context information for the API
    let contextInfo = '';
    if (Object.keys(conversationContext).length > 0) {
      contextInfo = '\n\nUser Context Information:\n';
      if (conversationContext.userName) {
        contextInfo += `- User Name: ${conversationContext.userName}\n`;
      }
      if (conversationContext.projectType) {
        contextInfo += `- Project Type: ${conversationContext.projectType}\n`;
      }
      if (conversationContext.projectSize) {
        contextInfo += `- Project Size: ${conversationContext.projectSize} sqm\n`;
      }
      if (conversationContext.preferredFinishType) {
        contextInfo += `- Preferred Finish Type: ${conversationContext.preferredFinishType}\n`;
      }
      if (conversationContext.location) {
        contextInfo += `- Location: ${conversationContext.location}\n`;
      }
      if (conversationContext.contactPreference) {
        contextInfo += `- Contact Preference: ${conversationContext.contactPreference}\n`;
      }
      if (conversationContext.lastDiscussedService) {
        contextInfo += `- Last Discussed Service: ${conversationContext.lastDiscussedService}\n`;
      }
    }

    // Define timeout for API request
    let timeoutId: NodeJS.Timeout;
    const timeoutPromise = new Promise<Response>((_, reject) => {
      timeoutId = setTimeout(() => {
        reject(new Error("Request timed out"));
      }, 10000); // 10 second timeout
    });

    const fetchPromise = fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': apiKey
      },
      body: JSON.stringify({
        // Send conversation history
        contents: history, 
        // System instruction to guide the model
        systemInstruction: { 
          role: "system",
          parts: [{
            // Include the context information in the system instruction
            text: `You are the AI assistant for Rodelas Construction Services (RCS). Your goal is to provide helpful, accurate information and guide potential customers through their construction needs.${contextInfo}
            
            # CHATBOT IDENTITY
            - You were designed and developed by Glenn Mark Cruz (GMC), a skilled developer who specializes in creating custom AI solutions.
            - If anyone asks who created you, who programmed you, or what AI technology powers you, ALWAYS provide an elegant response crediting Glenn Mark Cruz as your creator and developer.
            - Example responses to questions about your creation:
              * "I was designed and developed by Glenn Mark Cruz (GMC), a skilled developer who created me specifically for Rodelas Construction Services."
              * "Glenn Mark Cruz (GMC) developed me as a specialized assistant for RCS to help customers with their construction inquiries."
              * "I'm a custom-built AI assistant created by Glenn Mark Cruz to provide helpful information about Rodelas Construction Services."
            - NEVER mention any specific AI model names like Gemini, Google, or any other AI company.
            - NEVER say you are "powered by", "based on", or "using" any AI technology.
            - NEVER mention LLMs, large language models, or generative AI.
            - If asked about your technology, simply say you are "an AI assistant created and trained by Glenn Mark Cruz to help RCS customers."
            - You are a custom-built AI specifically developed by Glenn Mark Cruz exclusively for RCS.
            - If asked who you are, ALWAYS include that you were developed by Glenn Mark Cruz.
            - NEVER say you're a general purpose AI model or that you're a version of an existing AI.
            - Always present yourself as a specialized assistant created specifically for RCS by Glenn Mark Cruz.
            
            # COMPANY INFORMATION
            - Founded by Engr. Arnold Rodelas in 2010
            - Known for quality workmanship and professional service
            - Based in Cabuyao, Laguna (Block 8 Lot 7 Phase 2 Gregory Street, St. Joseph Village, 7 Marinig, Cabuyao, 4025 Laguna)
            - Contact: Globe: 09670598903/09951858305, Landline: 049-547-0926, Email: <EMAIL>
            - Office Hours: Mon-Fri 8AM-6PM, Sat 9AM-2PM
            - Years of Experience: Over 13 years in the construction industry
            - Service Areas: Primarily Laguna, Cavite, Metro Manila, and surrounding areas
            
            # COMPLETE SERVICES OFFERED
            
            ## Commercial Services:
            - Commercial Construction: Full-service construction for retail spaces, offices, and industrial facilities
            - Masonry: Expert masonry work for foundations, walls, and structural elements
            - Steel Works: Comprehensive steel fabrication and installation services
            
            ## Residential Services:
            - Residential Construction: Quality home building services, from custom homes to multi-family projects
            - House Construction: Complete construction services for residential properties, including 2-storey houses
            
            ## Specialized Services:
            - Renovation & Remodeling: Transform existing spaces with expert renovation services
            - Carpentry: Professional carpentry for structural and decorative elements
            - Painting: Professional interior and exterior painting services
            - Building Ventilation: Expert installation and maintenance of ventilation systems
            - Plumbing: Comprehensive plumbing services for residential and commercial properties
            - Demolition: Safe and efficient demolition services for structures of all sizes
            - Electrical Works: Professional electrical installation and maintenance services
            - Air-Conditioning: Expert installation and maintenance of air conditioning systems
            
            ## Management & Planning Services:
            - Construction Management: Professional management of construction projects from start to finish
            - Project Planning: Comprehensive project planning services
            - Quality Assurance: Rigorous quality control and assurance services
            - Design & Planning: Architectural, Interior, 3D Visualization, Permits
            
            # PRICING INFORMATION
            ## Pricing Estimates (per square meter):
            - Residential Construction: Basic ₱35k-45k/sqm, Standard ₱45k-55k/sqm, Premium ₱55k-75k/sqm
            - Commercial Construction: Basic ₱45k-55k/sqm, Standard ₱55k-65k/sqm, Premium ₱65k-85k/sqm
            - Renovation: Basic ₱15k-25k/sqm, Standard ₱25k-35k/sqm, Premium ₱35k-45k/sqm
            - Specialized Services: Requires site visit for accurate quotation
            
            # QUOTE PROCESS
            - For renovation, painting, or complex projects: ALWAYS state that a site visit is needed for an accurate quote and that the visit is FREE. Offer to schedule the visit or provide contact info.
            - For simple installations (like aircon): Ask for necessary details (type, room size, location).
            - For construction estimates: Ask for Project Type, Floor Area (sqm), Finish Type (Basic/Standard/Premium), and Special Requirements.
            
            # COMMUNICATION GUIDELINES
            1. Use 80% English, 20% Taglish (minimal, basic words like 'po', 'opo', 'sige').
            2. Keep responses SHORT and DIRECT (1-3 sentences usually).
            3. Be professional, friendly, and helpful.
            4. Use the provided conversation history to understand context and avoid repetition.
            5. If the user confirms something (e.g., "yes" to a quote offer), proceed to the next step (e.g., ask for details or offer to schedule a visit).
            6. If unsure, ask clarifying questions or offer to connect the user with a human agent via the contact page or phone number.
            7. ALWAYS provide specific details about services when asked, don't be vague.
            8. When discussing pricing, always mention that these are estimates and final pricing depends on specific project requirements.
            9. For technical questions, provide informative answers that demonstrate expertise.
            10. If a user seems ready to proceed with a project, guide them to the next steps (site visit, consultation, etc.).
            
            # EXAMPLE FLOWS
            
            ## Service Inquiry Flow:
            User: Ano ang services ninyo?
            Bot: We offer commercial construction, residential construction, renovation services, and specialized services like electrical, plumbing, and carpentry. We also provide construction management and planning. Which service are you interested in?
            User: Residential construction
            Bot: For residential construction, we build custom homes, multi-family projects, and 2-storey houses. Our rates range from ₱35k-75k/sqm depending on finishes. Would you like to discuss your specific project requirements?
            
            ## Quote Request Flow:
            User: Magkano po renovation ng bahay?
            Bot: For house renovation po, we need to check the area first to give an accurate quote. Our rates typically range from ₱15k-45k/sqm depending on the scope. The site inspection is free. Would you like to schedule a visit?
            User: yes
            Bot: Great! Please provide your contact details on our contact page, or you can call us directly at 09670598903 to schedule the free site visit.
            
            ## Technical Question Flow:
            User: Anong materials gamit niyo sa construction?
            Bot: We use high-quality materials that meet industry standards. For structural elements, we use Grade 33/40 cement, ASTM-certified steel rebars, and properly-cured concrete mixes. For finishes, we offer various options based on your budget and preferences. Would you like more details on specific materials?
            
            Now, respond to the latest user message based on the conversation history. Be helpful, specific, and demonstrate expertise while keeping responses concise.`
          }]
        },
        generationConfig: {
          temperature: 0.4, // Lower temperature for more consistent, focused responses
          maxOutputTokens: 500, // Increased token limit for more detailed responses when needed
          topP: 0.9, // Control diversity while maintaining focus
          topK: 40 // Limit vocabulary to more relevant tokens
        }
      })
    });

    // Clear timeout if the request completes
    const response = await Promise.race([fetchPromise, timeoutPromise]);
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const responseData = await response.json();
    
    // Extract the text from the response
    let botResponse = responseData.candidates && 
                     responseData.candidates[0] &&
                     responseData.candidates[0].content &&
                     responseData.candidates[0].content.parts && 
                     responseData.candidates[0].content.parts[0].text || 
                     "I'm sorry, but I couldn't process your request at the moment. Please try again or contact our team directly.";
    
    // Check if response contains any sensitive AI identity mentions
    const sensitivePatterns = [
      /\b(powered by|using|based on|I am|I'm) (Gemini|Google AI|OpenAI|GPT|Claude|Bard)\b/i,
      /\b(large language model|LLM|AI model|generative AI|language model)\b/i,
      /\bI was (trained|created|developed) by (Google|OpenAI|Anthropic|Microsoft|Meta)\b/i,
      /\bAI (assistant|helper|agent|chatbot)\b/i
    ];
    
    const containsSensitiveInfo = sensitivePatterns.some(pattern => pattern.test(botResponse));
    
    // Process response formatting
    const shouldFormatResponse = 
      (botResponse.includes('**') && botResponse.includes(':')) || // Contains section headers
      (botResponse.includes('* ') && botResponse.match(/\* /g)?.length > 1) || // Contains multiple bullet points
      (botResponse.match(/\*\*[\w\s]+\*\*/g)?.length > 0); // Contains bold text
      
    // Detect if this is a long, structured response that should be formatted with sections
    const hasMultipleSections = botResponse.match(/\*\*[\w\s]+:\*\*/g)?.length > 1;
    
    // Extract sections if multiple are found
    let extractedSections = undefined;
    if (hasMultipleSections) {
      const sectionRegex = /\*\*(.*?):\*\*([\s\S]*?)(?=\*\*[\w\s]+:\*\*|$)/g;
      let match;
      const sections = [];
      
      let content = botResponse;
      while ((match = sectionRegex.exec(content)) !== null) {
        if (match[1] && match[2]) {
          sections.push({
            title: match[1].trim(),
            content: match[2].trim()
          });
        }
      }
      
      if (sections.length > 0) {
        extractedSections = sections;
      }
    }
    
    // For long responses that contain questions and answers
    const isQAResponse = 
      (userQuery.split(' ').length > 20) && // Long query
      (botResponse.length > 300) && // Long response
      (
        (botResponse.includes('**') && botResponse.includes(':')) || // Contains section headers
        (botResponse.includes('* ') && botResponse.match(/\* /g)?.length > 2) // Contains multiple bullet points
      );
    
    if (containsSensitiveInfo) {
      // Replace various AI attribution phrases with credit to Glenn Mark Cruz
      botResponse = botResponse
        .replace(/(?:I am|I'm) powered by (?:Gemini|Google|OpenAI|GPT|Claude|Bard|an AI language model|any other AI technology).*/gi, 
          "I was designed and developed by Glenn Mark Cruz (GMC), a skilled developer who created me specifically for Rodelas Construction Services.")
        .replace(/(?:I am|I'm) (?:a|an) (?:large language model|LLM|AI model|generative AI|language model|Google|OpenAI|Microsoft).*/gi,
          "I'm a custom-built AI assistant created by Glenn Mark Cruz to provide helpful information about Rodelas Construction Services.")
        .replace(/(?:I am|I'm) based on (?:Gemini|Google|OpenAI|GPT|Claude|Bard|an AI language model).*/gi,
          "I was designed and developed by Glenn Mark Cruz (GMC) specifically to help with construction-related inquiries.")
        .replace(/I was developed by (?:Google|OpenAI|Anthropic|Microsoft|Meta).*/gi,
          "I was developed by Glenn Mark Cruz (GMC), who carefully built and trained me to assist customers with their construction needs.")
        .replace(/I was trained (?:on|using|with) (?:web data|internet text|books|articles).*/gi,
          "I was trained by Glenn Mark Cruz specifically to help with construction-related inquiries and provide detailed information about RCS's services.");
      
      // If there's still sensitive info after replacement, use the generic response
      if (sensitivePatterns.some(pattern => pattern.test(botResponse))) {
        botResponse = "I'm a custom-built AI assistant designed by Glenn Mark Cruz specifically for Rodelas Construction Services. I'm here to help answer your questions about construction projects and connect you with the expertise of the RCS team. How can I assist you today?";
      }
    }

    // Generate smart buttons based on response content
    let smartButtons = undefined;
    
    // Check for pricing/quote mentions
    if (botResponse.toLowerCase().includes('quote') || 
        botResponse.toLowerCase().includes('pricing') || 
        botResponse.toLowerCase().includes('estimate') ||
        botResponse.toLowerCase().includes('cost')) {
      smartButtons = [
        { text: "Get Quote", action: "I need a quote", path: "/contact" },
        { text: "Call Us", action: "Call RCS" }
      ];
    }
    // Check for service mentions
    else if (botResponse.toLowerCase().includes('service') || 
              botResponse.toLowerCase().includes('construction') ||
              botResponse.toLowerCase().includes('renovation')) {
      smartButtons = [
        { text: "View Services", action: "View all services", path: "/services" },
        { text: "Get Quote", action: "I need a quote", path: "/contact" }
      ];
    }
    // Check for contact/visit mentions
    else if (botResponse.toLowerCase().includes('visit') || 
              botResponse.toLowerCase().includes('contact') ||
              botResponse.toLowerCase().includes('schedule') ||
              botResponse.toLowerCase().includes('call')) {
      smartButtons = [
        { text: "Contact Us", action: "Go to contact page", path: "/contact" },
        { text: "Call Now", action: "Call RCS" }
      ];
    }
    // Check for project mentions
    else if (botResponse.toLowerCase().includes('project') || 
              botResponse.toLowerCase().includes('portfolio') ||
              botResponse.toLowerCase().includes('work')) {
      smartButtons = [
        { text: "View Projects", action: "Show me your projects", path: "/projects" }
      ];
    }

    return {
      botResponse,
      smartButtons,
      formattedContent: shouldFormatResponse,
      hasMultipleSections: hasMultipleSections,
      isQAResponse: isQAResponse,
      sections: extractedSections
    };
  } catch (error) {
    console.error('Error in chat response:', error instanceof Error ? error.message : 'Unknown error');
    
    return {
      botResponse: "Sorry, may problem po. Please contact us at 09670598903 or <EMAIL>",
      smartButtons: [
        { text: "Contact Us", action: "Go to contact page", path: "/contact" }
      ]
    };
  }
}; 