import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Search, Filter, Upload, X } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/database';
import { FileUpload, ImagePreview } from '@/components/admin/FileUpload';
import { SimpleStorageService as StorageService, UploadResult } from '@/lib/storage-simple';
import type { Project, ProjectInsert, ProjectUpdate } from '@/lib/database.types';

interface ProjectFormData {
  title: string;
  description: string;
  category: string;
  location: string;
  completion_date: string;
  featured_image: string;
  gallery_images: string;
  client: string;
  status: string;
  is_featured: boolean;
}

interface UploadedImage {
  url: string;
  path: string;
}

const ProjectsAdmin = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [formData, setFormData] = useState<ProjectFormData>({
    title: '',
    description: '',
    category: 'Residential',
    location: '',
    completion_date: '',
    featured_image: 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Project',
    gallery_images: '',
    client: '',
    status: 'completed',
    is_featured: false
  });
  const [uploadedFeaturedImage, setUploadedFeaturedImage] = useState<UploadedImage | null>(null);
  const [uploadedGalleryImages, setUploadedGalleryImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const { toast } = useToast();

  const categories = ['Commercial', 'Residential', 'Renovation', 'Infrastructure'];
  const statuses = ['completed', 'in_progress', 'planned'];

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await db.projects.getAll(true); // Include unpublished for admin
      setProjects(projectsData);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch projects',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate required fields
      if (!formData.title) {
        toast({
          title: 'Validation Error',
          description: 'Please enter a project title',
          variant: 'destructive'
        });
        return;
      }

      // Simple data preparation
      const projectData = {
        title: formData.title,
        description: formData.description || '',
        category: formData.category || 'Residential',
        location: formData.location || '',
        completion_date: formData.completion_date || null,
        status: formData.status || 'completed',
        featured_image: formData.featured_image || 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Project',
        gallery_images: formData.gallery_images || '',
        client: formData.client || '',
        is_featured: formData.is_featured || false
      };

      console.log('Submitting project data:', projectData);

      if (editingProject) {
        // Update existing project
        console.log('Updating project with ID:', editingProject.id);
        const result = await db.projects.update(editingProject.id, projectData as ProjectUpdate);
        console.log('Update result:', result);
        toast({
          title: 'Success',
          description: 'Project updated successfully'
        });
      } else {
        // Create new project
        console.log('Creating new project...');
        const result = await db.projects.create(projectData as ProjectInsert);
        console.log('Create result:', result);
        toast({
          title: 'Success',
          description: 'Project created successfully'
        });
      }

      setIsDialogOpen(false);
      resetForm();
      fetchProjects();
    } catch (error) {
      console.error('Error saving project:', error);
      toast({
        title: 'Error',
        description: `Failed to save project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive'
      });
    }
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    setFormData({
      title: project.title,
      description: project.description,
      category: project.category,
      location: project.location,
      completion_date: project.completion_date,
      featured_image: project.featured_image || '',
      gallery_images: project.gallery_images || [],
      client: project.client || '',
      status: project.status,
      is_featured: project.is_featured,
      tags: project.tags || [],
      features: project.features || [],
      highlights: project.highlights || []
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this project?')) return;
    
    try {
      await db.projects.delete(id);
      toast({
        title: 'Success',
        description: 'Project deleted successfully'
      });
      fetchProjects();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete project',
        variant: 'destructive'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: 'Residential',
      location: '',
      completion_date: '',
      featured_image: 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Project',
      gallery_images: '',
      client: '',
      status: 'completed',
      is_featured: false
    });
    setEditingProject(null);
  };

  const handleFeaturedImageUpload = (results: UploadResult[]) => {
    if (results.length > 0 && results[0].url) {
      setUploadedFeaturedImage({
        url: results[0].url,
        path: results[0].path
      });
      setFormData(prev => ({ ...prev, featured_image: results[0].url }));
    }
  };

  const handleGalleryImagesUpload = (results: UploadResult[]) => {
    const newImages = results
      .filter(result => result.url)
      .map(result => ({
        url: result.url,
        path: result.path
      }));

    setUploadedGalleryImages(prev => [...prev, ...newImages]);
    setFormData(prev => ({
      ...prev,
      gallery_images: [...prev.gallery_images, ...newImages.map(img => img.url)]
    }));
  };

  const removeFeaturedImage = () => {
    setUploadedFeaturedImage(null);
    setFormData(prev => ({ ...prev, featured_image: '' }));
  };

  const removeGalleryImage = (index: number) => {
    setUploadedGalleryImages(prev => prev.filter((_, i) => i !== index));
    setFormData(prev => ({
      ...prev,
      gallery_images: prev.gallery_images.filter((_, i) => i !== index)
    }));
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || project.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, { title: '', description: '' }]
    }));
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const updateFeature = (index: number, field: 'title' | 'description', value: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((feature, i) => 
        i === index ? { ...feature, [field]: value } : feature
      )
    }));
  };

  const addHighlight = () => {
    setFormData(prev => ({
      ...prev,
      highlights: [...prev.highlights, '']
    }));
  };

  const removeHighlight = (index: number) => {
    setFormData(prev => ({
      ...prev,
      highlights: prev.highlights.filter((_, i) => i !== index)
    }));
  };

  const updateHighlight = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      highlights: prev.highlights.map((highlight, i) => 
        i === index ? value : highlight
      )
    }));
  };

  const addTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Projects Management</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="w-4 h-4 mr-2" />
              Add Project
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingProject ? 'Edit Project' : 'Add New Project'}
              </DialogTitle>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Project Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="completion_date">Completion Date</Label>
                  <Input
                    id="completion_date"
                    type="date"
                    value={formData.completion_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, completion_date: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="featured_image">Featured Image URL (Temporary)</Label>
                  <Input
                    id="featured_image"
                    value={formData.featured_image}
                    onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                    placeholder="https://via.placeholder.com/800x600/0066cc/ffffff?text=Sample+Project"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use placeholder URLs for now. Example: https://via.placeholder.com/800x600
                  </p>
                </div>

                <div>
                  <Label htmlFor="gallery_images">Gallery Images (Optional)</Label>
                  <Input
                    id="gallery_images"
                    value={formData.gallery_images}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      gallery_images: e.target.value
                    }))}
                    placeholder="https://via.placeholder.com/600x400, https://via.placeholder.com/600x400"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Separate multiple URLs with commas
                  </p>
                </div>

                <div>
                  <Label htmlFor="client">Client (Optional)</Label>
                  <Input
                    id="client"
                    value={formData.client}
                    onChange={(e) => setFormData(prev => ({ ...prev, client: e.target.value }))}
                    placeholder="Client name"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_featured"
                    checked={formData.is_featured}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
                  />
                  <Label htmlFor="is_featured">Featured Project</Label>
                </div>

                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="planned">Planned</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingProject ? 'Update Project' : 'Create Project'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>{category}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Projects Grid */}
      {loading ? (
        <div className="text-center py-8">Loading projects...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map(project => (
            <Card key={project.id} className="overflow-hidden">
              <div className="aspect-video bg-gray-200 relative">
                {project.featured_image && (
                  <img
                    src={project.featured_image}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute top-2 right-2 flex gap-1">
                  {project.is_featured && (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      Featured
                    </Badge>
                  )}
                  <Badge variant="outline">{project.category}</Badge>
                </div>
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-2">{project.title}</h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {project.description}
                </p>
                <div className="flex justify-between items-center">
                  <Badge variant={project.status === 'completed' ? 'default' : 'secondary'}>
                    {project.status}
                  </Badge>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleEdit(project)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleDelete(project.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectsAdmin;
