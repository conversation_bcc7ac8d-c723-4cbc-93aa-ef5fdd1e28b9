import React from 'react';
import { MessageSquare, Trash2, Plus, Clock, Search } from 'lucide-react';
import { ConversationContext } from './types';

interface ConversationHistory {
  id: string;
  title: string;
  timestamp: Date;
  messages: Array<{
    content: string;
    isBot: boolean;
    timestamp: Date;
  }>;
  context: ConversationContext;
}

interface HistoryPanelProps {
  conversations: ConversationHistory[];
  currentConversationId: string | null;
  onLoadConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onStartNewConversation: () => void;
  onClose: () => void;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  conversations,
  currentConversationId,
  onLoadConversation,
  onDeleteConversation,
  onStartNewConversation,
  onClose
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  // Sort conversations by most recent first
  const sortedConversations = [...conversations].sort((a, b) => 
    b.timestamp.getTime() - a.timestamp.getTime()
  );

  // Filter conversations by search term
  const filteredConversations = sortedConversations.filter(conv => 
    searchTerm.trim() === '' || 
    conv.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.messages.some(msg => msg.content.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Format date to readable format
  const formatDate = (date: Date) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const conversationDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    if (conversationDate.getTime() === today.getTime()) {
      return 'Today, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (conversationDate.getTime() === yesterday.getTime()) {
      return 'Yesterday, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ', ' + 
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  return (
    <div className="fixed inset-0 z-[10000] bg-black bg-opacity-50 flex justify-end">
      <div className="bg-white w-full max-w-sm md:max-w-md h-full p-4 shadow-lg flex flex-col animate-slideInRight">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg">Conversation History</h3>
          <button
            onClick={onClose}
            className="p-1.5 rounded-full hover:bg-gray-100"
            aria-label="Close history panel"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="relative mb-4">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search conversations..."
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          />
          <Search className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
        </div>
        
        <button
          onClick={onStartNewConversation}
          className="mb-4 w-full py-2 rounded-lg bg-blue-50 text-blue-600 hover:bg-blue-100 flex items-center justify-center gap-2 text-sm"
        >
          <Plus size={16} /> Start New Conversation
        </button>

        {filteredConversations.length === 0 ? (
          <div className="flex-grow flex flex-col items-center justify-center text-gray-500 text-center p-4">
            <Clock size={48} className="mb-3 opacity-50" />
            {searchTerm ? (
              <>
                <p className="font-medium">No conversations found</p>
                <p className="text-sm mt-1">Try a different search term</p>
              </>
            ) : (
              <>
                <p className="font-medium">No conversation history</p>
                <p className="text-sm mt-1">Your conversations will appear here</p>
              </>
            )}
          </div>
        ) : (
          <div className="flex-grow overflow-y-auto -mx-4 px-4 pb-4">
            {filteredConversations.map((conv) => (
              <div
                key={conv.id}
                className={`mb-3 p-3 rounded-lg border ${
                  currentConversationId === conv.id
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                } transition-colors cursor-pointer relative group`}
                onClick={() => onLoadConversation(conv.id)}
              >
                <div className="flex items-start mb-1">
                  <MessageSquare size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="flex-grow overflow-hidden">
                    <h4 className="font-medium text-gray-800 truncate">{conv.title}</h4>
                    <p className="text-xs text-gray-500 flex items-center gap-1 mt-0.5">
                      <Clock size={12} /> {formatDate(conv.timestamp)}
                    </p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteConversation(conv.id);
                    }}
                    className="p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-opacity"
                    aria-label="Delete conversation"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <p className="text-xs text-gray-600 line-clamp-2 ml-6">
                  {conv.messages[conv.messages.length - 1]?.content || ''}
                </p>
              </div>
            ))}
          </div>
        )}
        
        {conversations.length > 0 && (
          <div className="mt-2 px-1 text-xs text-gray-500">
            {conversations.length} conversation{conversations.length !== 1 ? 's' : ''}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPanel;

// Add this to your global css or as inline styles
const styles = `
.animate-slideInRight {
  animation: slideInRight 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
`; 