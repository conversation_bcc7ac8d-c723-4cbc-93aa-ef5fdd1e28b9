-- Supabase Storage Setup for Rodelas Construction Services CMS
-- This script sets up storage buckets and policies for file management

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
    ('project-images', 'project-images', true),
    ('service-icons', 'service-icons', true),
    ('blog-images', 'blog-images', true),
    ('media-files', 'media-files', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for project-images bucket
CREATE POLICY "Admin can upload project images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'project-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can update project images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'project-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can delete project images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'project-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Public can view project images" ON storage.objects
    FOR SELECT USING (bucket_id = 'project-images');

-- Storage policies for service-icons bucket
CREATE POLICY "Admin can upload service icons" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'service-icons' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can update service icons" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'service-icons' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can delete service icons" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'service-icons' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Public can view service icons" ON storage.objects
    FOR SELECT USING (bucket_id = 'service-icons');

-- Storage policies for blog-images bucket
CREATE POLICY "Admin can upload blog images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'blog-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can update blog images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'blog-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can delete blog images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'blog-images' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Public can view blog images" ON storage.objects
    FOR SELECT USING (bucket_id = 'blog-images');

-- Storage policies for media-files bucket
CREATE POLICY "Admin can upload media files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'media-files' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can update media files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'media-files' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Admin can delete media files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'media-files' AND
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id::text = auth.uid()::text
        )
    );

CREATE POLICY "Public can view media files" ON storage.objects
    FOR SELECT USING (bucket_id = 'media-files');
