import React, { useEffect, useRef, useState } from 'react';
import { Shield, Clock, Award, Users, Check, Building, Home } from 'lucide-react';
import { motion } from 'framer-motion';

interface StatCounterProps {
  value: number;
  label: string;
  suffix?: string;
  delay: number;
}

const StatCounter: React.FC<StatCounterProps> = ({ value, label, suffix = "", delay }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
          }, delay);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => {
      if (counterRef.current) {
        observer.unobserve(counterRef.current);
      }
    };
  }, [delay]);

  useEffect(() => {
    if (!isVisible) return;

    let start = 0;
    const end = Math.min(value, 1000);
    const duration = 2000;
    const increment = end / (duration / 16);
    
    const timer = setInterval(() => {
      start += increment;
      setCount(Math.floor(start));
      if (start >= end) {
        setCount(end);
        clearInterval(timer);
      }
    }, 16);

    return () => {
      clearInterval(timer);
    };
  }, [isVisible, value]);

  return (
    <div 
      ref={counterRef}
      className={`flex flex-col items-center transition-all duration-500 transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
    >
      <p className="text-3xl md:text-4xl font-bold text-rcs-blue">
        {count}{suffix}
      </p>
      <p className="text-gray-600 text-sm md:text-base mt-1">{label}</p>
    </div>
  );
};

interface FeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  comparisonPoints: string[];
  badge?: string;
  delay: number;
}

const Feature: React.FC<FeatureProps> = ({ icon, title, description, comparisonPoints, badge, delay }) => {
  const [isVisible, setIsVisible] = useState(false);
  const featureRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
          }, delay);
        }
      },
      { threshold: 0.1 }
    );

    if (featureRef.current) {
      observer.observe(featureRef.current);
    }

    return () => {
      if (featureRef.current) {
        observer.unobserve(featureRef.current);
      }
    };
  }, [delay]);

  return (
    <div
      ref={featureRef}
      className={`flex flex-col text-center transition-all duration-500 transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      } bg-white rounded-lg shadow-sm p-6 relative`}
    >
      {badge && (
        <div className="absolute -top-2 -right-2">
          <span className="bg-rcs-gold text-rcs-blue text-xs font-bold px-2 py-1 rounded-full">
            {badge}
          </span>
        </div>
      )}
      <div className="bg-rcs-gold p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4 mx-auto">
        <div className="text-rcs-blue">{icon}</div>
      </div>
      <h3 className="text-xl font-bold text-rcs-blue mb-3">{title}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      
      <div className="mt-auto">
        <div className="border-t border-gray-100 pt-3 mt-3">
          <ul className="space-y-2 text-left">
            {comparisonPoints.map((point, index) => (
              <li key={index} className="flex items-start">
                <Check size={16} className="text-green-500 mr-2 mt-1 flex-shrink-0" />
                <span className="text-gray-600 text-sm">{point}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

const WhyChooseUs = () => {
  const stats = [
    { value: 13, label: "Years of Experience", suffix: "+", delay: 100 },
    { value: 25, label: "Projects Completed", suffix: "+", delay: 200 },
    { value: 98, label: "Client Satisfaction", suffix: "%", delay: 300 },
    { value: 8, label: "Expert Team Members", suffix: "+", delay: 400 },
  ];

  const features = [
    {
      icon: <Shield size={32} />,
      title: "Quality Guaranteed",
      description: "We deliver superior results that exceed expectations and stand the test of time.",
      badge: "Top Rated",
      comparisonPoints: [
        "Premium materials selection for durability",
        "Comprehensive quality assurance inspections",
        "Industry-leading construction standards"
      ],
      delay: 100
    },
    {
      icon: <Clock size={32} />,
      title: "On-Time Delivery",
      description: "Your projects delivered on schedule, eliminating delays and disruptions to your plans.",
      comparisonPoints: [
        "Detailed project timelines and milestones",
        "Regular progress updates and transparency",
        "Efficient resource management"
      ],
      delay: 200
    },
    {
      icon: <Award size={32} />,
      title: "Experienced Team",
      description: "Skilled professionals who bring expertise and precision to every aspect of your project.",
      badge: "Certified",
      comparisonPoints: [
        "Licensed professional engineers on staff",
        "Continuous training in modern techniques",
        "Specialized expertise across construction domains"
      ],
      delay: 300
    },
    {
      icon: <Users size={32} />,
      title: "Customer Satisfaction",
      description: "Your vision and requirements are our top priority throughout the entire process.",
      comparisonPoints: [
        "Personalized consultation approach",
        "Responsive communication channels",
        "Post-project support and follow-up"
      ],
      delay: 400
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-rcs-blue mb-4">Why Choose Us</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            With over a decade of experience in the construction industry, we've earned the trust of our clients through quality craftsmanship and reliable service.
          </p>
        </div>

        {/* Statistics Row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <StatCounter 
              key={index}
              value={stat.value}
              label={stat.label}
              suffix={stat.suffix}
              delay={stat.delay}
            />
          ))}
        </div>

        {/* Project Type Indicator */}
        <div className="flex justify-center mb-12 gap-4">
          <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
            <Building size={16} className="text-rcs-blue mr-2" />
            <span className="text-sm font-medium">Commercial</span>
          </div>
          <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
            <Home size={16} className="text-rcs-blue mr-2" />
            <span className="text-sm font-medium">Residential</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Feature
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              comparisonPoints={feature.comparisonPoints}
              badge={feature.badge}
              delay={feature.delay}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
