
#root {
  max-width: 100%;
  margin: 0 auto;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Responsive tabs */
.responsive-tabs {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.responsive-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

@media (max-width: 768px) {
  .responsive-tabs .tabs-list {
    min-width: max-content;
  }
}

/* Add styles for service category tabs */
.service-tabs {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 5px; /* Add some padding to avoid cutting off focus rings */
}

.service-tabs::-webkit-scrollbar {
  display: none;
}

.service-tabs-list {
  display: flex;
  min-width: max-content;
}

/* Ensure related services cards have consistent height */
.related-service-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.related-service-card .card-content {
  flex-grow: 1;
}

.related-service-card .card-footer {
  margin-top: auto;
}
