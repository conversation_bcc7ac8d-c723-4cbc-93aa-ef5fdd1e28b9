import { useState, useEffect, useRef } from 'react';
import { ContactFormData, sendContactEmail, validateEmail } from '@/lib/email-service';
import { useToast } from '@/hooks/use-toast';
import { getCsrfToken } from '@/lib/csrf-protection';

export const useContactForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<ContactFormData>({
    from_name: '',
    from_email: '',
    phone: '',
    message: '',
    csrfToken: '',
    recaptchaToken: ''
  });
  // Save scroll position before form submission
  const scrollPosRef = useRef(0);
  const { toast } = useToast();

  // Initialize CSRF token when component loads
  useEffect(() => {
    const token = getCsrfToken();
    setFormData(prev => ({
      ...prev,
      csrfToken: token
    }));
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const setRecaptchaToken = (token: string) => {
    setFormData(prev => ({
      ...prev,
      recaptchaToken: token
    }));
  };

  const resetForm = () => {
    // Get fresh CSRF token when form is reset
    const token = getCsrfToken();
    setFormData({
      from_name: '',
      from_email: '',
      phone: '',
      message: '',
      csrfToken: token,
      recaptchaToken: ''
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Store current scroll position before form submission
    scrollPosRef.current = window.scrollY;
    
    // Validation
    if (!formData.from_name || !formData.from_email || !formData.message) {
      toast({
        title: 'Please fill in all fields',
        description: 'Name, email, and message are required.',
        variant: 'destructive'
      });
      return;
    }

    // ReCAPTCHA validation
    if (!formData.recaptchaToken) {
      toast({
        title: 'ReCAPTCHA verification required',
        description: 'Please complete the reCAPTCHA verification.',
        variant: 'destructive'
      });
      return;
    }

    // Enhanced email validation
    if (!validateEmail(formData.from_email)) {
      // Determine what kind of validation error occurred
      const domain = formData.from_email.split('@')[1]?.toLowerCase() || '';
      
      let errorMsg = 'Invalid email address. ';
      
      if (domain.includes('tempmail') || domain.includes('mailinator') || 
          domain.includes('yopmail') || domain.includes('guerrilla')) {
        errorMsg += "We don't accept disposable or temporary email addresses.";
      } else if (domain && !["com", "net", "org", "edu", "gov", "ph"].includes(domain.split('.').pop() || '')) {
        errorMsg += "Please use a common domain extension like .com, .net, .org, or .ph.";
      } else {
        errorMsg += "Please provide a valid email address.";
      }
      
      toast({
        title: 'Email validation failed',
        description: errorMsg,
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await sendContactEmail(formData);
      
      if (result.success) {
        toast({
          title: 'Message sent!',
          description: result.message
        });
        resetForm();
        
        // Use setTimeout to allow browser to finish processing before restoring scroll
        setTimeout(() => {
          window.scrollTo({
            top: scrollPosRef.current,
            behavior: 'auto'
          });
        }, 0);
      } else {
        // If there was a CSRF token error, refresh the token and show error
        if (result.message.includes('security token') || result.message.includes('CSRF')) {
          // Get a fresh token
          const newToken = getCsrfToken();
          setFormData(prev => ({
            ...prev,
            csrfToken: newToken
          }));
        }
        
        toast({
          title: 'Error sending message',
          description: result.message,
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      
      // Make sure we maintain scroll position after setting isSubmitting to false
      setTimeout(() => {
        window.scrollTo({
          top: scrollPosRef.current,
          behavior: 'auto'
        });
      }, 10);
    }
  };

  return {
    formData,
    isSubmitting,
    handleChange,
    handleSubmit,
    resetForm,
    setRecaptchaToken,
    toast
  };
}; 