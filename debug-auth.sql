-- Debug authentication issues
-- Run this in Supabase SQL Editor to check user data

-- Check what users exist in auth.users table
SELECT id, email, created_at 
FROM auth.users 
ORDER BY created_at DESC;

-- Check what admin users exist
SELECT * FROM admin_users;

-- Check if there's a mismatch between auth.users and admin_users
SELECT 
    au.id as auth_id,
    au.email as auth_email,
    adu.id as admin_id,
    adu.email as admin_email,
    CASE 
        WHEN au.id::text = adu.id::text THEN 'MATCH'
        ELSE 'MISMATCH'
    END as id_status
FROM auth.users au
FULL OUTER JOIN admin_users adu ON au.id::text = adu.id::text;
