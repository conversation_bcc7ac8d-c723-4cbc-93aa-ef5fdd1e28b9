import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// Get current directory 
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Main directories to scan
const directories = [
  'public',
  'public/images',
  'public/images/projects',
  'public/images/projects/residential',
  'public/images/projects/commercial',
  'public/images/projects/renovation'
];

// Recursively get all subdirectories
function getAllDirs() {
  const result = [];
  
  function traverse(dir) {
    result.push(dir);
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        traverse(path.join(dir, entry.name));
      }
    }
  }
  
  for (const dir of directories) {
    if (fs.existsSync(dir)) {
      traverse(dir);
    }
  }
  
  return result;
}

// Process all JPG files in all directories
async function processImages() {
  const dirs = getAllDirs();
  console.log(`Found ${dirs.length} directories to scan`);
  
  let totalImages = 0;
  let convertedImages = 0;
  
  for (const dir of dirs) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      
      // Skip if it's a directory or not a JPG file
      if (fs.statSync(filePath).isDirectory() || 
          !file.toLowerCase().endsWith('.jpg')) {
        continue;
      }
      
      totalImages++;
      const outputPath = filePath.replace(/\.jpg$/i, '.webp');
      
      try {
        // Convert to WebP with quality 80 (good balance of quality and file size)
        await sharp(filePath)
          .webp({ quality: 80 })
          .toFile(outputPath);
          
        console.log(`Converted: ${filePath} -> ${outputPath}`);
        convertedImages++;
      } catch (error) {
        console.error(`Error converting ${filePath}:`, error);
      }
    }
  }
  
  console.log(`Conversion complete. Converted ${convertedImages} of ${totalImages} images.`);
}

processImages().catch(err => {
  console.error('Error in image processing:', err);
}); 