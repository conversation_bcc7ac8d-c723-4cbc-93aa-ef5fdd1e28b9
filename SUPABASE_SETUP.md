# Supabase Setup Guide for Rodelas Construction Services CMS

This guide will help you set up the Supabase backend for the CMS functionality.

## Step 1: Create Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Sign up or log in to your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: `rodelas-construction-cms`
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose the closest region to your users
6. Click "Create new project"
7. Wait for the project to be created (this may take a few minutes)

## Step 2: Get Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 3: Update Environment Variables

1. Open the `.env` file in your project root
2. Replace the placeholder values:
   ```env
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key-here
   ```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the contents of `supabase-schema.sql` and paste it into the editor
4. Click "Run" to execute the schema creation
5. Verify that all tables were created by going to **Table Editor**

## Step 5: Set Up Storage

1. In your Supabase dashboard, go to **Storage**
2. Go to **SQL Editor** again
3. Copy the contents of `supabase-storage.sql` and paste it into a new query
4. Click "Run" to create storage buckets and policies
5. Verify that the buckets were created in the **Storage** section

## Step 6: Create Admin User

1. Go to **Authentication** > **Users**
2. Click "Add user"
3. Fill in:
   - **Email**: Your admin email address
   - **Password**: Choose a strong password
   - **Email Confirm**: Set to true
4. Click "Create user"
5. Copy the user ID (UUID)

## Step 7: Add Admin User to Database

1. Go to **SQL Editor**
2. Run this query (replace `YOUR_USER_ID` with the UUID from step 6):
   ```sql
   INSERT INTO admin_users (id, email, role) 
   VALUES ('YOUR_USER_ID', '<EMAIL>', 'admin');
   ```

## Step 8: Configure Authentication

1. Go to **Authentication** > **Settings**
2. Under **Site URL**, add your domain (for development: `http://localhost:3000`)
3. Under **Redirect URLs**, add:
   - `http://localhost:3000/admin/dashboard` (for development)
   - `https://your-domain.com/admin/dashboard` (for production)

## Step 9: Test the Connection

1. Start your development server: `npm run dev`
2. Check the browser console for any Supabase connection errors
3. The CMS should now be able to connect to your Supabase backend

## Security Notes

- Never commit your Supabase credentials to version control
- Use environment variables for all sensitive data
- The anon key is safe to use in client-side code (it's designed for that)
- Row Level Security (RLS) policies protect your data
- Only authenticated admin users can modify content

## Troubleshooting

### Connection Issues
- Verify your environment variables are correct
- Check that your Supabase project is active
- Ensure you're using the correct project URL and anon key

### Authentication Issues
- Make sure the admin user exists in both Supabase Auth and the admin_users table
- Check that RLS policies are enabled
- Verify the user ID matches between Auth and admin_users table

### Storage Issues
- Ensure storage buckets are created
- Check that storage policies are properly set up
- Verify file upload permissions

## Next Steps

Once Supabase is set up, you can:
1. Access the admin panel at `/admin/login`
2. Start adding content through the CMS
3. Migrate existing static content to the database
4. Configure additional features like email notifications

For support, check the [Supabase documentation](https://supabase.com/docs) or contact the development team.
