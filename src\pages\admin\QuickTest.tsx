import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';

const QuickTest = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results = [];

    try {
      // Test 1: Basic connection
      console.log('Testing basic connection...');
      const { data: connectionTest, error: connectionError } = await supabase
        .from('projects')
        .select('count')
        .limit(1);
      
      results.push({
        test: 'Database Connection',
        status: connectionError ? 'FAILED' : 'PASSED',
        details: connectionError ? connectionError.message : 'Connection successful',
        data: connectionTest
      });

      // Test 2: Check table structure
      console.log('Checking table structure...');
      const { data: tableInfo, error: tableError } = await supabase
        .rpc('get_table_info', { table_name: 'projects' })
        .catch(() => {
          // Fallback: try to select from projects table
          return supabase.from('projects').select('*').limit(1);
        });

      results.push({
        test: 'Table Structure',
        status: tableError ? 'FAILED' : 'PASSED',
        details: tableError ? tableError.message : 'Table accessible',
        data: tableInfo
      });

      // Test 3: Try to fetch projects without published filter
      console.log('Testing projects fetch without filter...');
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .limit(5);

      results.push({
        test: 'Fetch Projects (No Filter)',
        status: projectsError ? 'FAILED' : 'PASSED',
        details: projectsError ? projectsError.message : `Found ${projectsData?.length || 0} projects`,
        data: projectsData
      });

      // Test 4: Check if published column exists
      console.log('Testing published column...');
      const { data: publishedTest, error: publishedError } = await supabase
        .from('projects')
        .select('published')
        .limit(1);

      results.push({
        test: 'Published Column Check',
        status: publishedError ? 'FAILED' : 'PASSED',
        details: publishedError ? publishedError.message : 'Published column exists',
        data: publishedTest
      });

      // Test 5: Storage bucket test
      console.log('Testing storage bucket...');
      const { data: buckets, error: bucketError } = await supabase.storage.listBuckets();
      
      results.push({
        test: 'Storage Buckets',
        status: bucketError ? 'FAILED' : 'PASSED',
        details: bucketError ? bucketError.message : `Found ${buckets?.length || 0} buckets`,
        data: buckets
      });

    } catch (error) {
      results.push({
        test: 'General Error',
        status: 'FAILED',
        details: error.message,
        data: null
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Quick Database Test</h1>
        <Button onClick={runTests} disabled={loading}>
          {loading ? 'Running Tests...' : 'Run Tests'}
        </Button>
      </div>

      <div className="space-y-4">
        {testResults.map((result, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {result.test}
                <Badge variant={result.status === 'PASSED' ? 'default' : 'destructive'}>
                  {result.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-2">{result.details}</p>
              {result.data && (
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {testResults.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">Click "Run Tests" to check your database connection and setup.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default QuickTest;
