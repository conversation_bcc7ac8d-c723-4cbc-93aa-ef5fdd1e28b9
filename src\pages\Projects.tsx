import React, { useEffect, useState, useRef } from 'react';
import { CalendarDays, MapPin, ArrowLeft, ArrowRight, Tag, CircleDot, ExternalLink, Award, Check } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import ProjectGallery from '../components/projects/ProjectGallery';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { OptimizedImage, getOptimizedImagePath } from '@/lib/imageUtils';
import { db } from '@/lib/database';
import { supabase } from '@/lib/supabase';
import type { Project } from '@/lib/database.types';

interface ProjectFeature {
  title: string;
  description: string;
}

const Projects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [expandedProject, setExpandedProject] = useState<string | null>(null);
  const [activeSectionTab, setActiveSectionTab] = useState("gallery");
  const projectRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

  // Fetch projects from database
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        console.log('Fetching projects for public page...');

        // Try direct Supabase query first
        const { data: directData, error: directError } = await supabase
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false });

        if (directError) {
          console.error('Direct query error:', directError);
          throw directError;
        }

        console.log('Direct query successful:', directData);
        setProjects(directData || []);

      } catch (error) {
        console.error('Error fetching projects:', error);
        console.error('Error details:', {
          message: error?.message,
          code: error?.code,
          details: error?.details
        });
        // Show a user-friendly message
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const categories = ["All", ...Array.from(new Set(projects.map(project => project.category)))];
  const filteredProjects = selectedCategory === "All" ? projects : projects.filter(project => project.category === selectedCategory);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Check if there's a hash in the URL 
    const hash = window.location.hash.substring(1);
    if (hash) {
      // First check if it's a category (case-insensitive)
      const categoryMatch = categories.find(
        category => category.toLowerCase() === hash.toLowerCase()
      );
      
      if (categoryMatch) {
        // If it's a category, set the category filter
        setSelectedCategory(categoryMatch);
        // Make sure hash matches exact category name for consistency
        if (hash !== categoryMatch) {
          window.history.replaceState(null, '', `#${categoryMatch}`);
        }
      } else {
        // Otherwise, it might be a project ID
        setExpandedProject(hash);
        // Scroll to the project after a short delay to allow rendering
        setTimeout(() => {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 200); // Slightly longer delay to ensure component is fully rendered
      }
    }
  }, [categories]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-rcs-blue mb-4">Loading Projects...</h1>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rcs-blue mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  const toggleProject = (projectId: string) => {
    if (expandedProject === projectId) {
      setExpandedProject(null);
      setActiveSectionTab("gallery"); // Reset to gallery tab when closing
      // Remove hash from URL
      window.history.pushState(null, '', window.location.pathname);
    } else {
      // First set the expanded project without scroll
      setExpandedProject(projectId);
      setActiveSectionTab("gallery"); // Reset to gallery tab when opening
      
      // Update URL hash without scrolling
      window.history.pushState(null, '', `#${projectId}`);
      
      // Scroll to the project with a smoother behavior and proper delay
      setTimeout(() => {
        const element = document.getElementById(projectId);
        if (element) {
          // Use smooth scrolling with a slight offset to prevent janky movement
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100); // Reduced timeout for a more responsive feel
    }
  };

  // Listen for back/forward navigation to handle expanded state
  useEffect(() => {
    const handlePopState = () => {
      const hash = window.location.hash.substring(1);
      if (hash) {
        // Check if it's a category
        const categoryMatch = categories.find(
          category => category.toLowerCase() === hash.toLowerCase()
        );
        
        if (categoryMatch) {
          // If it's a category, update the selected category
          setSelectedCategory(categoryMatch);
          // Also clear any expanded project
          setExpandedProject(null);
        } else {
          // If not a category, assume it's a project ID
          setExpandedProject(hash);
        }
      } else {
        // If no hash, clear both
        setSelectedCategory("All");
        setExpandedProject(null);
      }
    };
    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [categories]);

  return (
    <>
      <div className="pt-24 pb-20 bg-gradient-to-r from-rcs-blue to-rcs-blue/80">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl text-white mb-4 md:mb-6 shadow-text font-bold">Our Projects</h1>
            <p className="text-base md:text-xl text-white/90 shadow-text font-normal">
              Explore our portfolio of successful construction projects that demonstrate our expertise and commitment to excellence.
            </p>
          </div>
        </div>
      </div>

      <div className="py-8 md:py-12 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex justify-center mb-6 md:mb-8">
            <div className="flex flex-wrap justify-center gap-2">
              {categories.map(category => (
                <Button 
                  key={category} 
                  onClick={() => {
                    // Set the selected category
                    setSelectedCategory(category);
                    // Clear any expanded project when switching categories
                    if (expandedProject) {
                      setExpandedProject(null);
                    }
                    // Update URL hash to match the selected category
                    window.history.pushState(null, '', `#${category}`);
                  }} 
                  variant={selectedCategory === category ? "default" : "outline"}
                  className={`px-3 md:px-5 py-4 md:py-6 rounded-md transition-all duration-300 font-bold text-sm md:text-lg ${
                    selectedCategory === category 
                      ? 'bg-rcs-blue text-white shadow-md scale-105' 
                      : 'bg-white text-rcs-blue hover:bg-gray-100 border border-gray-200'
                  }`}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-8 md:space-y-12 mt-6 md:mt-8">
            {filteredProjects.map(project => (
              <motion.div
                key={project.id}
                id={project.id}
                ref={el => projectRefs.current[project.id] = el}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-white rounded-lg overflow-hidden shadow-lg border-0 transition-all duration-300 hover:shadow-xl">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="relative h-[300px] lg:h-auto overflow-hidden group">
                      <OptimizedImage
                        src={project.featured_image}
                        alt={project.title}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                        loading="lazy"
                      />
                      <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                        <Badge variant="secondary" className="bg-rcs-gold text-rcs-blue text-sm font-bold px-3 py-1">
                          {project.category}
                        </Badge>
                      </div>
                      
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                        <div className="p-6 w-full">
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleProject(project.id);
                            }} 
                            className="bg-white/90 hover:bg-white text-rcs-blue font-medium rounded-md px-4 py-2 transition-colors duration-200 inline-flex items-center"
                          >
                            <ExternalLink size={16} className="mr-2" /> View Project Details
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <CardContent className="p-4 md:p-8 flex flex-col justify-between">
                      <div>
                        <h3 className="text-xl md:text-3xl font-bold text-rcs-blue mb-2 md:mb-3">{project.title}</h3>
                        
                        <div className="flex flex-wrap gap-3 text-xs md:text-sm text-gray-500 mb-3 md:mb-4">
                          <div className="flex items-center">
                            <MapPin size={14} className="mr-1 text-rcs-gold" />
                            <span>{project.location}</span>
                          </div>
                          <div className="flex items-center">
                            <CalendarDays size={14} className="mr-1 text-rcs-gold" />
                            <span>Completed: {project.completionDate}</span>
                          </div>
                          {project.client && (
                            <div className="flex items-center">
                              <Award size={14} className="mr-1 text-rcs-gold" />
                              <span>Client: {project.client}</span>
                            </div>
                          )}
                        </div>
                        
                        <p className="text-sm md:text-base text-gray-700 mb-4 md:mb-6">
                          {expandedProject === project.id ? project.description : `${project.description.substring(0, 150)}${project.description.length > 150 ? '...' : ''}`}
                        </p>
                        
                        {project.tags && (
                          <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
                            {project.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs md:text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">
                                <Tag size={10} className="mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <Button 
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleProject(project.id);
                        }} 
                        variant="outline"
                        className="text-rcs-blue border border-rcs-gold/80 hover:bg-rcs-gold hover:text-rcs-blue transition-all duration-300 font-semibold group w-full md:w-auto"
                      >
                        {expandedProject === project.id ? (
                          <>View Less <ArrowLeft size={18} className="ml-2 transition-transform group-hover:-translate-x-1" /></>
                        ) : (
                          <>View Details <ArrowRight size={18} className="ml-2 transition-transform group-hover:translate-x-1" /></>
                        )}
                      </Button>
                    </CardContent>
                  </div>
                  
                  <AnimatePresence>
                    {expandedProject === project.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.4, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="p-6 pt-0 lg:p-8 lg:pt-0">
                          <hr className="my-6 border-gray-200" />
                          
                          <Tabs value={activeSectionTab} onValueChange={setActiveSectionTab} className="w-full">
                            <TabsList className="grid w-full grid-cols-3 mb-6 md:mb-8">
                              <TabsTrigger value="gallery" className="text-sm md:text-lg">Gallery</TabsTrigger>
                              <TabsTrigger value="details" className="text-sm md:text-lg">Details</TabsTrigger>
                                <TabsTrigger value="features" className="text-sm md:text-lg">Features</TabsTrigger>
                            </TabsList>
                            
                            <TabsContent value="gallery" className="mt-0">
                              <ProjectGallery
                                images={[project.featured_image, ...(project.gallery_images as string[] || [])]}
                                title={project.title}
                              />
                            </TabsContent>
                            
                            <TabsContent value="details" className="mt-0">
                              <div className="bg-gray-50 rounded-lg p-4 md:p-6 shadow-inner">
                                <h4 className="text-lg md:text-xl font-bold text-rcs-blue mb-3 md:mb-4">About This Project</h4>
                                <p className="text-sm md:text-base text-gray-700 whitespace-pre-line">{project.description}</p>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mt-6 md:mt-8">
                                  <div className="bg-white rounded-lg p-4 md:p-5 shadow-sm">
                                    <h5 className="font-bold text-base md:text-lg mb-3 text-rcs-blue">Project Information</h5>
                                    <ul className="space-y-2 md:space-y-3 text-sm md:text-base">
                                      <li className="flex items-center">
                                        <CircleDot size={16} className="text-rcs-gold mr-2" />
                                        <span className="font-semibold">Category:</span>
                                        <span className="ml-2">{project.category}</span>
                                      </li>
                                      <li className="flex items-center">
                                        <CircleDot size={16} className="text-rcs-gold mr-2" />
                                        <span className="font-semibold">Location:</span>
                                        <span className="ml-2">{project.location}</span>
                                      </li>
                                      <li className="flex items-center">
                                        <CircleDot size={16} className="text-rcs-gold mr-2" />
                                        <span className="font-semibold">Completion:</span>
                                        <span className="ml-2">{new Date(project.completion_date).getFullYear()}</span>
                                      </li>
                                      {project.client && (
                                        <li className="flex items-center">
                                          <CircleDot size={16} className="text-rcs-gold mr-2" />
                                          <span className="font-semibold">Client:</span>
                                          <span className="ml-2">{project.client}</span>
                                        </li>
                                      )}
                                    </ul>
                                  </div>
                                  
                                  {project.tags && (
                                    <div className="bg-white rounded-lg p-4 md:p-5 shadow-sm">
                                      <h5 className="font-bold text-base md:text-lg mb-3 text-rcs-blue">Project Tags</h5>
                                      <div className="flex flex-wrap gap-2">
                                        {(project.tags as string[]).map((tag, index) => (
                                          <Badge key={index} className="text-xs md:text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">
                                            {tag}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </TabsContent>
                            
                            <TabsContent value="features" className="mt-0">
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                {project.features && (
                                  <div className="bg-gray-50 rounded-lg p-4 md:p-6 shadow-inner">
                                    <h4 className="text-lg md:text-xl font-bold text-rcs-blue mb-3 md:mb-4">Key Features</h4>
                                    <ul className="space-y-3 md:space-y-4">
                                      {(project.features as ProjectFeature[]).map((feature, index) => (
                                        <li key={index} className="bg-white rounded-md p-3 md:p-4 shadow-sm">
                                          <h5 className="font-bold text-base md:text-lg text-rcs-blue mb-1">{feature.title}</h5>
                                          <p className="text-sm md:text-base text-gray-700">{feature.description}</p>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                                
                                {project.highlights && (
                                  <div className="bg-gray-50 rounded-lg p-4 md:p-6 shadow-inner">
                                    <h4 className="text-lg md:text-xl font-bold text-rcs-blue mb-3 md:mb-4">Project Highlights</h4>
                                    <ul className="space-y-2">
                                      {(project.highlights as string[]).map((highlight, index) => (
                                        <li key={index} className="flex items-start">
                                          <Check size={20} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                                          <span>{highlight}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </TabsContent>
                          </Tabs>
                          
                          <div className="flex justify-center mt-8">
                            <Button 
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleProject(project.id);
                              }} 
                              className="bg-rcs-gold text-rcs-blue hover:bg-yellow-400 transition-colors duration-300 font-semibold"
                            >
                              Close Details <ArrowLeft size={18} className="ml-2" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Projects;
