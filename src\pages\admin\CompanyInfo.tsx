import React, { useState, useEffect } from 'react';
import { Save, Plus, Edit, Trash2, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/database';
import type { CompanyInfo, CompanyInfoUpdate } from '@/lib/database.types';

interface TeamMember {
  id?: string;
  name: string;
  position: string;
  bio: string;
  image: string;
  email?: string;
  phone?: string;
}

interface CompanyData {
  company_name: string;
  tagline: string;
  description: string;
  mission: string;
  vision: string;
  values: string[];
  address: string;
  phone: string;
  email: string;
  website: string;
  social_media: {
    facebook?: string;
    instagram?: string;
    linkedin?: string;
    twitter?: string;
  };
  business_hours: string;
  founded_year: number;
  team_members: TeamMember[];
  certifications: string[];
  awards: string[];
}

const CompanyInfoAdmin = () => {
  const [companyData, setCompanyData] = useState<CompanyData>({
    company_name: 'Rodelas Construction Services',
    tagline: 'Building Excellence, Delivering Dreams',
    description: '',
    mission: '',
    vision: '',
    values: [],
    address: '',
    phone: '',
    email: '',
    website: '',
    social_media: {},
    business_hours: '',
    founded_year: new Date().getFullYear(),
    team_members: [],
    certifications: [],
    awards: []
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isTeamDialogOpen, setIsTeamDialogOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [memberForm, setMemberForm] = useState<TeamMember>({
    name: '',
    position: '',
    bio: '',
    image: '',
    email: '',
    phone: ''
  });
  
  const { toast } = useToast();

  useEffect(() => {
    fetchCompanyInfo();
  }, []);

  const fetchCompanyInfo = async () => {
    try {
      setLoading(true);
      // Try to get existing company info
      const companyInfo = await db.companyInfo.getByField('company_data');
      if (companyInfo) {
        setCompanyData(companyInfo.field_value as CompanyData);
      }
    } catch (error) {
      console.log('No existing company info found, using defaults');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Save or update company info
      await db.companyInfo.upsert({
        field_name: 'company_data',
        field_value: companyData,
        field_type: 'json'
      });
      
      toast({
        title: 'Success',
        description: 'Company information saved successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save company information',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof CompanyData, value: any) => {
    setCompanyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSocialMediaChange = (platform: string, value: string) => {
    setCompanyData(prev => ({
      ...prev,
      social_media: {
        ...prev.social_media,
        [platform]: value
      }
    }));
  };

  const addValue = () => {
    setCompanyData(prev => ({
      ...prev,
      values: [...prev.values, '']
    }));
  };

  const updateValue = (index: number, value: string) => {
    setCompanyData(prev => ({
      ...prev,
      values: prev.values.map((v, i) => i === index ? value : v)
    }));
  };

  const removeValue = (index: number) => {
    setCompanyData(prev => ({
      ...prev,
      values: prev.values.filter((_, i) => i !== index)
    }));
  };

  const addCertification = () => {
    setCompanyData(prev => ({
      ...prev,
      certifications: [...prev.certifications, '']
    }));
  };

  const updateCertification = (index: number, value: string) => {
    setCompanyData(prev => ({
      ...prev,
      certifications: prev.certifications.map((c, i) => i === index ? value : c)
    }));
  };

  const removeCertification = (index: number) => {
    setCompanyData(prev => ({
      ...prev,
      certifications: prev.certifications.filter((_, i) => i !== index)
    }));
  };

  const handleTeamMemberSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingMember) {
      // Update existing member
      setCompanyData(prev => ({
        ...prev,
        team_members: prev.team_members.map(member => 
          member.id === editingMember.id ? { ...memberForm, id: editingMember.id } : member
        )
      }));
    } else {
      // Add new member
      setCompanyData(prev => ({
        ...prev,
        team_members: [...prev.team_members, { ...memberForm, id: Date.now().toString() }]
      }));
    }
    
    setIsTeamDialogOpen(false);
    resetMemberForm();
  };

  const editTeamMember = (member: TeamMember) => {
    setEditingMember(member);
    setMemberForm(member);
    setIsTeamDialogOpen(true);
  };

  const deleteTeamMember = (memberId: string) => {
    setCompanyData(prev => ({
      ...prev,
      team_members: prev.team_members.filter(member => member.id !== memberId)
    }));
  };

  const resetMemberForm = () => {
    setMemberForm({
      name: '',
      position: '',
      bio: '',
      image: '',
      email: '',
      phone: ''
    });
    setEditingMember(null);
  };

  if (loading) {
    return <div className="p-6 text-center">Loading company information...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Company Information</h1>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      <Tabs defaultValue="basic" className="space-y-6">
        <TabsList>
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="about">About Us</TabsTrigger>
          <TabsTrigger value="contact">Contact</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company_name">Company Name</Label>
                  <Input
                    id="company_name"
                    value={companyData.company_name}
                    onChange={(e) => handleInputChange('company_name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="founded_year">Founded Year</Label>
                  <Input
                    id="founded_year"
                    type="number"
                    value={companyData.founded_year}
                    onChange={(e) => handleInputChange('founded_year', parseInt(e.target.value))}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="tagline">Tagline</Label>
                <Input
                  id="tagline"
                  value={companyData.tagline}
                  onChange={(e) => handleInputChange('tagline', e.target.value)}
                  placeholder="Your company tagline"
                />
              </div>
              
              <div>
                <Label htmlFor="description">Company Description</Label>
                <Textarea
                  id="description"
                  value={companyData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  placeholder="Brief description of your company"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Mission, Vision & Values</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="mission">Mission Statement</Label>
                <Textarea
                  id="mission"
                  value={companyData.mission}
                  onChange={(e) => handleInputChange('mission', e.target.value)}
                  rows={3}
                  placeholder="Your company's mission"
                />
              </div>
              
              <div>
                <Label htmlFor="vision">Vision Statement</Label>
                <Textarea
                  id="vision"
                  value={companyData.vision}
                  onChange={(e) => handleInputChange('vision', e.target.value)}
                  rows={3}
                  placeholder="Your company's vision"
                />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <Label>Company Values</Label>
                  <Button type="button" size="sm" onClick={addValue}>
                    <Plus className="w-4 h-4 mr-1" />
                    Add Value
                  </Button>
                </div>
                <div className="space-y-2">
                  {companyData.values.map((value, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={value}
                        onChange={(e) => updateValue(index, e.target.value)}
                        placeholder="Company value"
                      />
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => removeValue(index)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={companyData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={2}
                  placeholder="Company address"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={companyData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Phone number"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={companyData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Email address"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={companyData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="Website URL"
                  />
                </div>
                <div>
                  <Label htmlFor="business_hours">Business Hours</Label>
                  <Input
                    id="business_hours"
                    value={companyData.business_hours}
                    onChange={(e) => handleInputChange('business_hours', e.target.value)}
                    placeholder="e.g., Mon-Fri 8AM-5PM"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Social Media</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input
                      id="facebook"
                      value={companyData.social_media.facebook || ''}
                      onChange={(e) => handleSocialMediaChange('facebook', e.target.value)}
                      placeholder="Facebook URL"
                    />
                  </div>
                  <div>
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input
                      id="instagram"
                      value={companyData.social_media.instagram || ''}
                      onChange={(e) => handleSocialMediaChange('instagram', e.target.value)}
                      placeholder="Instagram URL"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Team Members</CardTitle>
                <Dialog open={isTeamDialogOpen} onOpenChange={setIsTeamDialogOpen}>
                  <DialogTrigger asChild>
                    <Button onClick={resetMemberForm}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Team Member
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingMember ? 'Edit Team Member' : 'Add Team Member'}
                      </DialogTitle>
                    </DialogHeader>
                    
                    <form onSubmit={handleTeamMemberSubmit} className="space-y-4">
                      <div>
                        <Label htmlFor="member_name">Name</Label>
                        <Input
                          id="member_name"
                          value={memberForm.name}
                          onChange={(e) => setMemberForm(prev => ({ ...prev, name: e.target.value }))}
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="member_position">Position</Label>
                        <Input
                          id="member_position"
                          value={memberForm.position}
                          onChange={(e) => setMemberForm(prev => ({ ...prev, position: e.target.value }))}
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="member_bio">Bio</Label>
                        <Textarea
                          id="member_bio"
                          value={memberForm.bio}
                          onChange={(e) => setMemberForm(prev => ({ ...prev, bio: e.target.value }))}
                          rows={3}
                        />
                      </div>
                      
                      <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => setIsTeamDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button type="submit">
                          {editingMember ? 'Update' : 'Add'} Member
                        </Button>
                      </div>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {companyData.team_members.map(member => (
                  <Card key={member.id}>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-3"></div>
                        <h3 className="font-semibold">{member.name}</h3>
                        <p className="text-sm text-gray-600">{member.position}</p>
                        <p className="text-xs text-gray-500 mt-2">{member.bio}</p>
                        <div className="flex justify-center space-x-2 mt-3">
                          <Button size="sm" variant="outline" onClick={() => editTeamMember(member)}>
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => deleteTeamMember(member.id!)}>
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="achievements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Certifications & Awards</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <Label>Certifications</Label>
                  <Button type="button" size="sm" onClick={addCertification}>
                    <Plus className="w-4 h-4 mr-1" />
                    Add Certification
                  </Button>
                </div>
                <div className="space-y-2">
                  {companyData.certifications.map((cert, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={cert}
                        onChange={(e) => updateCertification(index, e.target.value)}
                        placeholder="Certification name"
                      />
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => removeCertification(index)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompanyInfoAdmin;
