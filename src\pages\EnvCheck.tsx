import React from 'react';

const EnvCheck = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Environment Variables Check</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Supabase URL:</h3>
        <p style={{ 
          background: supabaseUrl ? '#d4edda' : '#f8d7da', 
          padding: '10px', 
          borderRadius: '5px',
          color: supabaseUrl ? '#155724' : '#721c24'
        }}>
          {supabaseUrl ? `✅ ${supabaseUrl}` : '❌ Missing VITE_SUPABASE_URL'}
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Supabase Anon Key:</h3>
        <p style={{ 
          background: supabaseKey ? '#d4edda' : '#f8d7da', 
          padding: '10px', 
          borderRadius: '5px',
          color: supabaseKey ? '#155724' : '#721c24'
        }}>
          {supabaseKey ? `✅ Present (${supabaseKey.substring(0, 20)}...)` : '❌ Missing VITE_SUPABASE_ANON_KEY'}
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>All Environment Variables:</h3>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '5px',
          overflow: 'auto',
          maxHeight: '300px'
        }}>
          {JSON.stringify(import.meta.env, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Instructions:</h3>
        <div style={{ background: '#fff3cd', padding: '15px', borderRadius: '5px', color: '#856404' }}>
          <p><strong>If environment variables are missing:</strong></p>
          <ol>
            <li>Create a <code>.env</code> file in your project root</li>
            <li>Add these lines:</li>
            <pre style={{ background: '#f8f9fa', padding: '10px', margin: '10px 0' }}>
{`VITE_SUPABASE_URL=https://krsdfvwwoiiqigyhjkep.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here`}
            </pre>
            <li>Restart your development server</li>
            <li>Refresh this page</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default EnvCheck;
