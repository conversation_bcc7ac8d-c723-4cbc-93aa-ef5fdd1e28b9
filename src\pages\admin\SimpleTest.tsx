import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';

const SimpleTest = () => {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testDirectQuery = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing direct Supabase query...');
      
      // Direct query without any filtering
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error:', error);
        setError(`Database Error: ${error.message} (Code: ${error.code})`);
        return;
      }

      console.log('Direct query result:', data);
      setResult(data);

    } catch (err) {
      console.error('Unexpected error:', err);
      setError(`Unexpected Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing basic connection...');
      
      // Test basic connection
      const { data, error } = await supabase
        .from('projects')
        .select('count')
        .limit(1);

      if (error) {
        console.error('Connection error:', error);
        setError(`Connection Error: ${error.message}`);
        return;
      }

      console.log('Connection test result:', data);
      setResult({ message: 'Connection successful!', data });

    } catch (err) {
      console.error('Connection failed:', err);
      setError(`Connection Failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Simple Database Test</h1>
        <div className="space-x-2">
          <Button onClick={testConnection} disabled={loading}>
            Test Connection
          </Button>
          <Button onClick={testDirectQuery} disabled={loading}>
            Get All Projects
          </Button>
        </div>
      </div>

      {loading && (
        <Card>
          <CardContent className="text-center py-8">
            <p>Testing database...</p>
          </CardContent>
        </Card>
      )}

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Database Result</CardTitle>
          </CardHeader>
          <CardContent>
            {Array.isArray(result) ? (
              <div>
                <p className="mb-4"><strong>Found {result.length} projects:</strong></p>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {result.map((project, index) => (
                    <div key={project.id || index} className="p-3 border rounded bg-gray-50">
                      <h3 className="font-medium">{project.title || 'No title'}</h3>
                      <p className="text-sm text-gray-600">
                        Category: {project.category || 'N/A'} | 
                        Location: {project.location || 'N/A'} | 
                        Status: {project.status || 'N/A'}
                      </p>
                      <p className="text-xs text-gray-400">
                        ID: {project.id} | Created: {project.created_at || 'N/A'}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>
      )}

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>First click "Test Connection" to verify database connectivity</li>
            <li>Then click "Get All Projects" to see all projects in your database</li>
            <li>Check the browser console (F12) for detailed logs</li>
            <li>If you see projects here but not in the main app, there's a filtering issue</li>
            <li>If you don't see projects here, run the SQL test script in Supabase</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleTest;
