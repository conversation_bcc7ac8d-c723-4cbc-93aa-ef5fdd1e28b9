import React from 'react';
import { MessageCircle } from 'lucide-react';

interface ChatToggleButtonProps {
  isOpen: boolean;
  toggleChat: () => void;
}

const ChatToggleButton: React.FC<ChatToggleButtonProps> = ({ isOpen, toggleChat }) => {
  return (
    <button
      onClick={toggleChat}
      className={`fixed bottom-6 right-6 z-40 bg-gradient-to-r from-rcs-blue to-blue-700 text-white rounded-full p-4 shadow-2xl hover:shadow-blue-300/30 transition-all duration-500 flex items-center justify-center gap-2 ${
        isOpen ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
      } hover:scale-105`}
      aria-label="Open chat"
      style={{ backdropFilter: 'blur(8px)' }}
    >
      <div className="relative">
        <MessageCircle className="h-6 w-6" />
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white shadow-md shadow-green-500/30"></span>
      </div>
      <span className="hidden md:inline text-sm font-medium">Chat with us</span>
    </button>
  );
};

export default ChatToggleButton; 