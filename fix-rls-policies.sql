-- =====================================================
-- CRITICAL FIX: RLS Policies and Storage Setup
-- Run this in Supabase SQL Editor to fix all errors
-- =====================================================

-- 1. DISABLE RLS temporarily to allow admin operations
ALTER TABLE projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE services DISABLE ROW LEVEL SECURITY;
ALTER TABLE company_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE media_files DISABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users DISABLE ROW LEVEL SECURITY;

-- 2. CREATE STORAGE BUCKET (if not exists)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'rcs-media',
  'rcs-media',
  true,
  10485760, -- 10MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf']
)
ON CONFLICT (id) DO NOTHING;

-- 3. STORAGE POLICIES - Allow public access for now
CREATE POLICY "Public Access" ON storage.objects FOR ALL USING (bucket_id = 'rcs-media');
CREATE POLICY "Public Upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'rcs-media');
CREATE POLICY "Public Update" ON storage.objects FOR UPDATE USING (bucket_id = 'rcs-media');
CREATE POLICY "Public Delete" ON storage.objects FOR DELETE USING (bucket_id = 'rcs-media');

-- 4. ENABLE RLS on storage
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

-- 5. BUCKET POLICIES
CREATE POLICY "Public bucket access" ON storage.buckets FOR ALL USING (true);

-- 6. SIMPLE RLS POLICIES FOR TABLES (Allow all for admin)
-- Projects
CREATE POLICY "Allow all operations" ON projects FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Services  
CREATE POLICY "Allow all operations" ON services FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE services ENABLE ROW LEVEL SECURITY;

-- Company Info
CREATE POLICY "Allow all operations" ON company_info FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE company_info ENABLE ROW LEVEL SECURITY;

-- Blog Posts
CREATE POLICY "Allow all operations" ON blog_posts FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Media Files
CREATE POLICY "Allow all operations" ON media_files FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Admin Users
CREATE POLICY "Allow all operations" ON admin_users FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 7. GRANT PERMISSIONS
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Storage permissions
GRANT ALL ON storage.objects TO anon, authenticated;
GRANT ALL ON storage.buckets TO anon, authenticated;

-- 8. CREATE ADMIN USER (if not exists)
INSERT INTO admin_users (email, password_hash, role, is_active)
VALUES (
  '<EMAIL>',
  '$2a$10$rZ8Q8Q8Q8Q8Q8Q8Q8Q8Q8O', -- placeholder hash
  'admin',
  true
)
ON CONFLICT (email) DO NOTHING;

-- 9. INSERT DEFAULT COMPANY INFO (if not exists)
INSERT INTO company_info (field_name, field_value)
VALUES 
  ('company_name', 'Rodelas Construction Services'),
  ('company_description', 'Professional construction services in the Philippines'),
  ('company_email', '<EMAIL>'),
  ('company_phone', '+63 ************'),
  ('company_address', 'Philippines')
ON CONFLICT (field_name) DO UPDATE SET field_value = EXCLUDED.field_value;

-- 10. VERIFY SETUP
SELECT 'Tables created:' as status;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

SELECT 'Storage bucket created:' as status;
SELECT * FROM storage.buckets WHERE id = 'rcs-media';

SELECT 'RLS policies created:' as status;
SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public';

-- 11. TEST QUERIES
SELECT 'Testing projects table:' as status;
SELECT COUNT(*) as project_count FROM projects;

SELECT 'Testing services table:' as status;
SELECT COUNT(*) as service_count FROM services;

SELECT 'Testing company_info table:' as status;
SELECT COUNT(*) as company_info_count FROM company_info;
