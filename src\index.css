@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 20%; /* #003366 Deep Blue */
    --primary-foreground: 210 40% 98%;

    --secondary: 51 100% 50%; /* #FFD700 Gold */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 0 0% 90%; /* #E5E5E5 Light Gray */
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 0 0% 90%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 210 100% 20%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 51 100% 50%;
    --secondary-foreground: 0 0% 12%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
  }
}

/* Utility classes for fonts */
@layer utilities {
  .font-poppins {
    font-family: 'Poppins', sans-serif;
  }
  .font-montserrat {
    font-family: 'Montserrat', sans-serif;
  }
}

html {
  scroll-behavior: smooth;
}

.hero-gradient {
  background: linear-gradient(90deg, rgba(0, 51, 102, 0.9) 0%, rgba(0, 51, 102, 0.7) 100%);
}

.service-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.project-card {
  overflow: hidden;
}

.project-card img {
  transition: transform 0.5s ease;
}

.project-card:hover img {
  transform: scale(1.05);
}

.project-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.floating-button {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* New styles for the Services page */
.shadow-text {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.service-category-tabs .active-tab {
  background-color: #003366;
  color: white;
}

/* Page transition animations */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 500ms, transform 500ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 500ms, transform 500ms;
}

/* New animations for page elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease forwards;
}

.animate-fadeInRight {
  animation: fadeInRight 0.6s ease forwards;
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

/* Custom animation for chat button */
@keyframes pulse-slow {
  0% { box-shadow: 0 0 0 0 rgba(0, 51, 102, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(0, 51, 102, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 51, 102, 0); }
}

.animate-pulse-slow {
  animation: pulse-slow 3s infinite;
}

/* Custom scrollbar styles for chat */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Animation classes */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Hamburger menu animations */
.hamburger-icon {
  position: relative;
  transition: all 0.3s ease;
}

/* Mobile menu animations */
.mobile-menu {
  overflow: hidden;
}

.mobile-menu-enter {
  max-height: 0;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1);
}

.mobile-menu-enter-active {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-exit {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-exit-active {
  max-height: 0;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1);
}

/* Mobile menu item animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.mobile-menu-item {
  animation: slideInRight 0.3s ease forwards;
  opacity: 0;
}

.mobile-menu-item:nth-child(1) {
  animation-delay: 0.1s;
}

.mobile-menu-item:nth-child(2) {
  animation-delay: 0.2s;
}

.mobile-menu-item:nth-child(3) {
  animation-delay: 0.3s;
}

.mobile-menu-item:nth-child(4) {
  animation-delay: 0.4s;
}

/* Hamburger icon transformation */
.hamburger-bar {
  display: block;
  width: 24px;
  height: 2px;
  margin: 5px 0;
  transition: all 0.3s ease-in-out;
  background-color: currentColor;
}

.hamburger-open .hamburger-bar:nth-child(1) {
  transform: translateY(7px) rotate(45deg);
}

.hamburger-open .hamburger-bar:nth-child(2) {
  opacity: 0;
}

.hamburger-open .hamburger-bar:nth-child(3) {
  transform: translateY(-7px) rotate(-45deg);
}
