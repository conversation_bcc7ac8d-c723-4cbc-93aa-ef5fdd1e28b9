import React, { useState, useEffect, memo } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  loadingStrategy?: 'lazy' | 'eager';
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  sizes?: string;
  fetchPriority?: 'high' | 'low' | 'auto';
}

/**
 * OptimizedImage - A performance-optimized image component
 * 
 * Features:
 * - Lazy loading with native browser support
 * - Blur-up loading effect for smoother user experience
 * - Proper image sizing with width/height to prevent layout shifts
 * - Priority loading for critical above-the-fold images
 * - Fallback handling for failed image loads
 * - Automatic WebP format usage with format detection
 * - Uses intersection observer for more efficient lazy loading
 */
const OptimizedImage = memo(({
  src,
  alt,
  width,
  height,
  className,
  loadingStrategy = 'lazy',
  priority = false,
  placeholder = 'empty',
  blurDataURL = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=',
  onLoad,
  onError,
  sizes = '100vw',
  fetchPriority = 'auto',
  ...props
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const imageRef = React.useRef<HTMLImageElement>(null);
  
  // Use eager loading for priority images
  const loading = priority ? 'eager' : loadingStrategy;
  
  // Set fetch priority for important images
  const imageFetchPriority = priority ? 'high' : fetchPriority;
  
  // Handle successful image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };
  
  // Handle image loading error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };
  
  // Use intersection observer for more efficient lazy loading
  useEffect(() => {
    if (!priority && imageRef.current && 'IntersectionObserver' in window) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              setIsVisible(true);
              observer.disconnect();
            }
          });
        },
        { rootMargin: '200px' }
      );
      
      observer.observe(imageRef.current);
      
      return () => {
        observer.disconnect();
      };
    } else {
      setIsVisible(true);
    }
  }, [priority]);
  
  // Preload priority images
  useEffect(() => {
    if (priority && src) {
      const preloadLink = document.createElement('link');
      preloadLink.rel = 'preload';
      preloadLink.as = 'image';
      preloadLink.href = src;
      document.head.appendChild(preloadLink);
      
      return () => {
        document.head.removeChild(preloadLink);
      };
    }
  }, [src, priority]);
  
  // If image failed to load, show fallback
  if (hasError) {
    return (
      <div 
        className={cn(
          'bg-gray-100 flex items-center justify-center', 
          className
        )}
        style={{ width, height }}
        role="img"
        aria-label={alt}
      >
        <svg 
          className="w-6 h-6 text-gray-400" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
          />
        </svg>
      </div>
    );
  }
  
  // Handle WebP format
  const getImageSources = () => {
    if (!src || src.startsWith('data:') || src.startsWith('blob:')) {
      return null;
    }
    
    const webpSrc = src.replace(/\.(jpe?g|png)$/i, '.webp');
    
    return (
      <>
        <source 
          srcSet={webpSrc} 
          type="image/webp" 
        />
        <source 
          srcSet={src} 
          type={src.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg'} 
        />
      </>
    );
  };
  
  return (
    <div className={cn('relative overflow-hidden', className)} style={{ width, height }}>
      {/* Blur placeholder */}
      {placeholder === 'blur' && !isLoaded && (
        <div 
          className="absolute inset-0 bg-no-repeat bg-cover blur-md scale-110 transform"
          style={{ 
            backgroundImage: `url(${blurDataURL})`,
            filter: 'blur(20px)',
            transition: 'opacity 0.5s ease-in-out',
            opacity: isLoaded ? 0 : 1 
          }}
          aria-hidden="true"
        />
      )}
      
      {/* Use picture element for format negotiation */}
      <picture>
        {getImageSources()}
        {/* Actual image */}
        <img
          ref={imageRef}
          src={isVisible || priority ? src : blurDataURL}
          alt={alt}
          width={width}
          height={height}
          loading={loading}
          decoding="async"
          fetchPriority={imageFetchPriority}
          sizes={sizes}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'max-w-full h-auto transition-opacity duration-500',
            { 'opacity-0': !isLoaded && placeholder === 'blur' },
            { 'opacity-100': isLoaded }
          )}
          style={{ 
            objectFit: 'cover',
            width: width ? `${width}px` : '100%',
            height: height ? `${height}px` : 'auto',
          }}
          {...props}
        />
      </picture>
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

export { OptimizedImage }; 