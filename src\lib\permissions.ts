import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { AdminUser } from './database.types';

// Define available permissions
export enum Permission {
  // Project management
  VIEW_PROJECTS = 'view_projects',
  CREATE_PROJECTS = 'create_projects',
  EDIT_PROJECTS = 'edit_projects',
  DELETE_PROJECTS = 'delete_projects',
  PUBLISH_PROJECTS = 'publish_projects',

  // Service management
  VIEW_SERVICES = 'view_services',
  CREATE_SERVICES = 'create_services',
  EDIT_SERVICES = 'edit_services',
  DELETE_SERVICES = 'delete_services',
  PUBLISH_SERVICES = 'publish_services',

  // Company information
  VIEW_COMPANY_INFO = 'view_company_info',
  EDIT_COMPANY_INFO = 'edit_company_info',

  // Blog management
  VIEW_BLOG_POSTS = 'view_blog_posts',
  CREATE_BLOG_POSTS = 'create_blog_posts',
  EDIT_BLOG_POSTS = 'edit_blog_posts',
  DELETE_BLOG_POSTS = 'delete_blog_posts',
  PUBLISH_BLOG_POSTS = 'publish_blog_posts',

  // Media management
  VIEW_MEDIA = 'view_media',
  UPLOAD_MEDIA = 'upload_media',
  DELETE_MEDIA = 'delete_media',

  // User management
  VIEW_USERS = 'view_users',
  CREATE_USERS = 'create_users',
  EDIT_USERS = 'edit_users',
  DELETE_USERS = 'delete_users',

  // System administration
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_SETTINGS = 'manage_settings',
  EXPORT_DATA = 'export_data',
  IMPORT_DATA = 'import_data',
}

// Define user roles
export enum Role {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  EDITOR = 'editor',
  AUTHOR = 'author',
  VIEWER = 'viewer',
}

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.SUPER_ADMIN]: Object.values(Permission), // All permissions

  [Role.ADMIN]: [
    // Project management
    Permission.VIEW_PROJECTS,
    Permission.CREATE_PROJECTS,
    Permission.EDIT_PROJECTS,
    Permission.DELETE_PROJECTS,
    Permission.PUBLISH_PROJECTS,

    // Service management
    Permission.VIEW_SERVICES,
    Permission.CREATE_SERVICES,
    Permission.EDIT_SERVICES,
    Permission.DELETE_SERVICES,
    Permission.PUBLISH_SERVICES,

    // Company information
    Permission.VIEW_COMPANY_INFO,
    Permission.EDIT_COMPANY_INFO,

    // Blog management
    Permission.VIEW_BLOG_POSTS,
    Permission.CREATE_BLOG_POSTS,
    Permission.EDIT_BLOG_POSTS,
    Permission.DELETE_BLOG_POSTS,
    Permission.PUBLISH_BLOG_POSTS,

    // Media management
    Permission.VIEW_MEDIA,
    Permission.UPLOAD_MEDIA,
    Permission.DELETE_MEDIA,

    // Analytics
    Permission.VIEW_ANALYTICS,
  ],

  [Role.EDITOR]: [
    // Project management
    Permission.VIEW_PROJECTS,
    Permission.CREATE_PROJECTS,
    Permission.EDIT_PROJECTS,
    Permission.PUBLISH_PROJECTS,

    // Service management
    Permission.VIEW_SERVICES,
    Permission.CREATE_SERVICES,
    Permission.EDIT_SERVICES,
    Permission.PUBLISH_SERVICES,

    // Company information
    Permission.VIEW_COMPANY_INFO,
    Permission.EDIT_COMPANY_INFO,

    // Blog management
    Permission.VIEW_BLOG_POSTS,
    Permission.CREATE_BLOG_POSTS,
    Permission.EDIT_BLOG_POSTS,
    Permission.PUBLISH_BLOG_POSTS,

    // Media management
    Permission.VIEW_MEDIA,
    Permission.UPLOAD_MEDIA,
  ],

  [Role.AUTHOR]: [
    // Project management (view only)
    Permission.VIEW_PROJECTS,

    // Service management (view only)
    Permission.VIEW_SERVICES,

    // Company information (view only)
    Permission.VIEW_COMPANY_INFO,

    // Blog management
    Permission.VIEW_BLOG_POSTS,
    Permission.CREATE_BLOG_POSTS,
    Permission.EDIT_BLOG_POSTS,

    // Media management
    Permission.VIEW_MEDIA,
    Permission.UPLOAD_MEDIA,
  ],

  [Role.VIEWER]: [
    // View-only permissions
    Permission.VIEW_PROJECTS,
    Permission.VIEW_SERVICES,
    Permission.VIEW_COMPANY_INFO,
    Permission.VIEW_BLOG_POSTS,
    Permission.VIEW_MEDIA,
  ],
};

// Permission checking utilities
export class PermissionManager {
  private static getUserRole(user: AdminUser | null): Role {
    if (!user) return Role.VIEWER;
    
    // Map database role to enum
    switch (user.role.toLowerCase()) {
      case 'super_admin':
        return Role.SUPER_ADMIN;
      case 'admin':
        return Role.ADMIN;
      case 'editor':
        return Role.EDITOR;
      case 'author':
        return Role.AUTHOR;
      default:
        return Role.VIEWER;
    }
  }

  public static hasPermission(user: AdminUser | null, permission: Permission): boolean {
    const role = this.getUserRole(user);
    const rolePermissions = ROLE_PERMISSIONS[role] || [];
    return rolePermissions.includes(permission);
  }

  public static hasAnyPermission(user: AdminUser | null, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  public static hasAllPermissions(user: AdminUser | null, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }

  public static getUserPermissions(user: AdminUser | null): Permission[] {
    const role = this.getUserRole(user);
    return ROLE_PERMISSIONS[role] || [];
  }

  public static canAccessResource(user: AdminUser | null, resource: string, action: string): boolean {
    const permission = `${action}_${resource}` as Permission;
    return this.hasPermission(user, permission);
  }
}

// React hooks for permission checking
export const usePermissions = () => {
  const { adminUser } = useAuth();

  return {
    hasPermission: (permission: Permission) => 
      PermissionManager.hasPermission(adminUser, permission),
    
    hasAnyPermission: (permissions: Permission[]) => 
      PermissionManager.hasAnyPermission(adminUser, permissions),
    
    hasAllPermissions: (permissions: Permission[]) => 
      PermissionManager.hasAllPermissions(adminUser, permissions),
    
    getUserPermissions: () => 
      PermissionManager.getUserPermissions(adminUser),
    
    canAccessResource: (resource: string, action: string) => 
      PermissionManager.canAccessResource(adminUser, resource, action),
    
    userRole: adminUser ? PermissionManager.getUserRole(adminUser) : Role.VIEWER,
  };
};

// Higher-order component for permission-based rendering
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission: Permission,
  fallback?: React.ComponentType<P>
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const { hasPermission } = usePermissions();

    if (!hasPermission(requiredPermission)) {
      if (fallback) {
        const FallbackComponent = fallback;
        return React.createElement(FallbackComponent, props);
      }
      return null;
    }

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPermission(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

// Component for conditional rendering based on permissions
interface PermissionGateProps {
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  children,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions.length > 0) {
    hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }

  return hasAccess ? React.createElement(React.Fragment, null, children) : React.createElement(React.Fragment, null, fallback);
};

// Utility functions for common permission checks
export const canManageProjects = (user: AdminUser | null): boolean => {
  return PermissionManager.hasAnyPermission(user, [
    Permission.CREATE_PROJECTS,
    Permission.EDIT_PROJECTS,
    Permission.DELETE_PROJECTS,
  ]);
};

export const canManageServices = (user: AdminUser | null): boolean => {
  return PermissionManager.hasAnyPermission(user, [
    Permission.CREATE_SERVICES,
    Permission.EDIT_SERVICES,
    Permission.DELETE_SERVICES,
  ]);
};

export const canManageBlog = (user: AdminUser | null): boolean => {
  return PermissionManager.hasAnyPermission(user, [
    Permission.CREATE_BLOG_POSTS,
    Permission.EDIT_BLOG_POSTS,
    Permission.DELETE_BLOG_POSTS,
  ]);
};

export const canManageMedia = (user: AdminUser | null): boolean => {
  return PermissionManager.hasAnyPermission(user, [
    Permission.UPLOAD_MEDIA,
    Permission.DELETE_MEDIA,
  ]);
};

export const isAdminOrHigher = (user: AdminUser | null): boolean => {
  const role = PermissionManager.getUserRole(user);
  return [Role.SUPER_ADMIN, Role.ADMIN].includes(role);
};
