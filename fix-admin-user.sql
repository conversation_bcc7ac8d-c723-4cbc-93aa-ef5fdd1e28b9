-- Fix admin user for the correct email
-- Run this in Supabase SQL Editor

-- First, let's see what users exist in auth.users
-- (This is just for reference, you can't run this in SQL editor)
-- SELECT id, email FROM auth.users;

-- Delete any existing admin users to avoid conflicts
DELETE FROM admin_users;

-- Insert the correct admin user with the right email and user ID
-- Replace the ID with your actual auth user ID from Supabase Auth
INSERT INTO admin_users (id, email, role) 
VALUES ('7d537368-f51c-4274-a55c-b53b8f190e5f', '<EMAIL>', 'admin')
ON CONFLICT (id) DO UPDATE SET 
    email = EXCLUDED.email,
    role = EXCLUDED.role;

-- Also add the rodelascons email if that's what you're using
INSERT INTO admin_users (id, email, role) 
VALUES ('7d537368-f51c-4274-a55c-b53b8f190e5f', '<EMAIL>', 'admin')
ON CONFLICT (id) DO UPDATE SET 
    email = EXCLUDED.email,
    role = EXCLUDED.role;

-- Check if the admin user was created
SELECT * FROM admin_users;
