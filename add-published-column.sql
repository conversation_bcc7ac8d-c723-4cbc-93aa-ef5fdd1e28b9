-- =====================================================
-- ADD PUBLISHED COLUMN TO EXISTING TABLES
-- Run this in your Supabase SQL Editor to add the missing published column
-- =====================================================

-- Add published column to projects table
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT true;

-- Add published column to services table  
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT true;

-- Add published column to blog_posts table (if it exists)
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT false;

-- Update existing projects to be published by default
UPDATE projects SET published = true WHERE published IS NULL;

-- Update existing services to be published by default  
UPDATE services SET published = true WHERE published IS NULL;

-- Verify the changes
SELECT 'Projects table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'projects' 
ORDER BY ordinal_position;

SELECT 'Services table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'services' 
ORDER BY ordinal_position;

-- Check if blog_posts table exists and show its structure
SELECT 'Blog posts table structure (if exists):' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'blog_posts' 
ORDER BY ordinal_position;
