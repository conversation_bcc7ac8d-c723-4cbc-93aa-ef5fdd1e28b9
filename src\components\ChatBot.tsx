import React, { useEffect, useRef } from 'react';
import ChatHeader from './chatbot/ChatHeader';
import ChatContainer from './chatbot/ChatContainer';
import ChatInput from './chatbot/ChatInput';
import ChatToggleButton from './chatbot/ChatToggleButton';
import MinimizedPreview from './chatbot/MinimizedPreview';
import HistoryPanel from './chatbot/HistoryPanel';
import { Archive, History, Sparkles } from 'lucide-react';
import { useChat } from './chatbot/hooks/useChat';

const ChatBot = () => {
  const { 
    isOpen,
    isMinimized,
    isExpanded,
    message,
    messages,
    isLoading,
    showHistory,
    savedConversations,
    currentConversationId,
    messagesEndRef,
    chatBoxRef,
    inputRef,
    toggleChat,
    toggleExpand,
    closeChat,
    toggleHistory,
    handleButtonClick,
    sendMessage,
    setMessage,
    saveCurrentConversation,
    loadConversation,
    startNewConversation,
    deleteConversation
  } = useChat();
  
  const inputAreaRef = useRef<HTMLDivElement>(null);

  // Add viewport height CSS variable for iOS devices
  useEffect(() => {
    const updateViewportHeight = () => {
      document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
    };
    
    updateViewportHeight();
    window.addEventListener('resize', updateViewportHeight);
    return () => window.removeEventListener('resize', updateViewportHeight);
  }, []);

  // Handle keyboard visibility on mobile - adjust chat container height
  useEffect(() => {
    if (!isOpen || isMinimized) return;

    const handleKeyboardChange = () => {
      if (inputAreaRef.current && inputRef.current) {
        setTimeout(() => {
          inputRef.current?.focus();
          // Ensure the chat input is visible when keyboard is open
          inputAreaRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }, 100);
      }
    };

    // Listen for resize (which might indicate keyboard opening/closing)
    window.addEventListener('resize', handleKeyboardChange);
    
    // Also handle focusing events
    inputRef.current?.addEventListener('focus', handleKeyboardChange);
    
    // Add click handler for the chat input area
    const handleChatInputClick = () => {
      // When user manually clicks on chat input, allow focusing
      if (inputRef.current) {
        inputRef.current.focus();
      }
    };
    
    // Add click listener to the input area
    inputAreaRef.current?.addEventListener('click', handleChatInputClick);
    
    return () => {
      window.removeEventListener('resize', handleKeyboardChange);
      inputRef.current?.removeEventListener('focus', handleKeyboardChange);
      inputAreaRef.current?.removeEventListener('click', handleChatInputClick);
    };
  }, [isOpen, isMinimized, inputRef, messages]);

  // Add global keyboard shortcuts
  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + / to toggle chat
      if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        toggleChat();
      }
      
      // Esc to close chat or history panel
      if (e.key === 'Escape') {
        if (showHistory) {
          toggleHistory();
        } else if (isOpen && !isMinimized) {
          toggleChat();
        }
      }
    };
    
    window.addEventListener('keydown', handleKeydown);
    return () => window.removeEventListener('keydown', handleKeydown);
  }, [toggleChat, isOpen, isMinimized, showHistory, toggleHistory]);

  // Add optimized CSS styles for chat
  useEffect(() => {
    // Create a style element
    const styleEl = document.createElement('style');
    
    // Add our optimized styles
    styleEl.textContent = `
      /* Improve performance with hardware acceleration */
      .animate-fadeIn {
        animation: fadeIn 0.3s ease-out forwards;
        will-change: opacity, transform;
        backface-visibility: hidden;
        transform: translateZ(0);
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .animate-pulse {
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }
      
      /* Safe area support for mobile devices */
      .pb-safe {
        padding-bottom: env(safe-area-inset-bottom, 1rem);
      }
      
      /* Improve input positioning with keyboard open */
      .chat-input-container {
        position: sticky;
        bottom: 0;
        background: white;
        z-index: 10;
        padding-bottom: env(safe-area-inset-bottom, 0.5rem);
        box-shadow: 0 -2px 5px rgba(0,0,0,0.05);
      }
      
      /* Fix for mobile chat height with keyboard open */
      .mobile-chat-fix {
        max-height: -webkit-fill-available;
        display: flex;
        flex-direction: column;
      }
      
      /* Fix for chat header - always visible */
      .chat-header-sticky {
        position: sticky;
        top: 0;
        z-index: 10;
        background: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      }
      
      /* Optimize scrollbars for the chat interface */
      .chat-scroll::-webkit-scrollbar {
        width: 6px;
      }
      
      .chat-scroll::-webkit-scrollbar-track {
        background: transparent;
      }
      
      .chat-scroll::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 20px;
      }
      
      /* Only show scrollbar on hover for better UX */
      .chat-scroll {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
      }
    `;
    
    // Add it to the document head
    document.head.appendChild(styleEl);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  return (
    <>
      <ChatToggleButton isOpen={isOpen} toggleChat={toggleChat} />

      {showHistory && (
        <HistoryPanel 
          conversations={savedConversations}
          currentConversationId={currentConversationId}
          onLoadConversation={loadConversation}
          onDeleteConversation={deleteConversation}
          onStartNewConversation={startNewConversation}
          onClose={toggleHistory}
        />
      )}

      <div
        ref={chatBoxRef}
        aria-live="polite"
        aria-label="Chat interface"
        role="region"
        className={`fixed z-[9999] transition-all duration-300 overflow-hidden flex flex-col bg-white
          ${isOpen ? 'opacity-100' : 'opacity-0 translate-y-10 pointer-events-none'}
          ${isMinimized ? 'h-auto' : 'mobile-chat-fix'}
          
          /* Mobile - Full screen using CSS variable height */
          ${!isMinimized ? 'top-0 left-0 right-0 bottom-0 w-full h-screen h-[calc(var(--vh,1vh)*100)]' : 'bottom-0 right-0 w-full h-auto'}
          
          /* Improved safe area insets for mobile devices */
          ${!isMinimized ? 'pb-[env(safe-area-inset-bottom)] pt-[env(safe-area-inset-top)]' : ''}
          
          /* Desktop */
          md:bottom-6 md:top-auto md:left-auto md:right-6 md:rounded-2xl md:shadow-2xl md:min-h-0
          ${isMinimized ? 'md:w-[320px]' : isExpanded ? 'md:h-[80vh] md:w-[600px]' : 'md:h-[600px] md:w-[400px]'}
          md:max-w-[95vw] md:border md:border-gray-200 md:border-opacity-50 md:backdrop-blur-sm md:backdrop-filter`}
        style={{
          boxShadow: '0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1)'
        }}
      >
        <div className="chat-header-sticky">
          <ChatHeader 
            isMinimized={isMinimized} 
            isExpanded={isExpanded}
            toggleChat={toggleChat} 
            toggleExpand={toggleExpand}
            closeChat={closeChat} 
          />

          {!isMinimized && (
            <div className="border-b border-gray-100 px-3 py-1.5 flex items-center justify-between">
              <div className="flex items-center gap-1 text-xs text-gray-500">
                {messages.length > 1 && (
                  <button
                    onClick={saveCurrentConversation}
                    className="p-1 rounded hover:bg-gray-100 flex items-center gap-1 text-gray-600"
                    title="Save conversation"
                    aria-label="Save conversation"
                  >
                    <Archive size={14} />
                    <span className="hidden md:inline">Save</span>
                  </button>
                )}
                <button
                  onClick={toggleHistory}
                  className="p-1 rounded hover:bg-gray-100 flex items-center gap-1 text-gray-600"
                  title="View conversation history"
                  aria-label="View conversation history"
                >
                  <History size={14} />
                  <span className="hidden md:inline">History</span>
                </button>
                <button
                  onClick={startNewConversation}
                  className="p-1 rounded hover:bg-gray-100 flex items-center gap-1 text-gray-600"
                  title="Start new conversation"
                  aria-label="Start new conversation"
                >
                  <Sparkles size={14} />
                  <span className="hidden md:inline">New Chat</span>
                </button>
              </div>
              
              {/* Add badge for current conversation if it exists */}
              {currentConversationId && (
                <span className="text-xs bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full">
                  Saved
                </span>
              )}
            </div>
          )}
        </div>

        {isMinimized && <MinimizedPreview messages={messages} />}

        {!isMinimized && (
          <>
            <ChatContainer 
              messages={messages} 
              isLoading={isLoading} 
              onButtonClick={handleButtonClick}
              messagesEndRef={messagesEndRef}
              className="pb-safe chat-scroll"
            />

            <div ref={inputAreaRef} className="chat-input-container">
              <ChatInput 
                message={message}
                setMessage={setMessage}
                sendMessage={sendMessage}
                isLoading={isLoading}
                inputRef={inputRef}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ChatBot;
