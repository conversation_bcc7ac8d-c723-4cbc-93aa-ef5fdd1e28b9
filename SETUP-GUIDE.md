# 🚀 RCS Admin CMS Setup Guide

## ⚠️ CRITICAL: Fix Database Issues First

### Step 1: Run SQL Setup in Supabase

1. **Go to your Supabase Dashboard**
2. **Navigate to SQL Editor**
3. **Copy and paste the entire content from `fix-rls-policies.sql`**
4. **Click "Run" to execute**

This will:
- ✅ Fix RLS (Row Level Security) policies
- ✅ Create storage bucket with proper permissions
- ✅ Set up all required tables
- ✅ Insert default data
- ✅ Grant necessary permissions

### Step 2: Create Storage Bucket Manually (if needed)

If the SQL script fails to create the bucket:

1. **Go to Supabase Dashboard → Storage**
2. **Click "New Bucket"**
3. **Bucket name**: `rcs-media`
4. **Make it Public**: ✅ Yes
5. **File size limit**: 10MB
6. **Allowed MIME types**: 
   - image/jpeg
   - image/png  
   - image/webp
   - image/gif

### Step 3: Verify Environment Variables

Make sure your `.env.local` has:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Step 4: Test the Admin Panel

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Navigate to**: `http://localhost:3000/admin`

3. **Login with**:
   - Email: `<EMAIL>`
   - Password: `admin123` (or create your own)

## 🧪 Testing Each Feature

### ✅ Projects Management
1. Go to `/admin/projects`
2. Click "Add Project"
3. Fill in:
   - Title: "Test Project"
   - Description: "Testing functionality"
   - Category: "Residential"
   - Location: "Test Location"
   - Completion Date: "2024-01-15"
4. Upload a featured image
5. Click "Create Project"

### ✅ Services Management
1. Go to `/admin/services`
2. Click "Add Service"
3. Fill in required fields
4. Click "Create Service"

### ✅ Blog Management
1. Go to `/admin/blog`
2. Click "Add Post"
3. Fill in title and content
4. Upload featured image
5. Set status to "Published"
6. Click "Create Post"

### ✅ Media Library
1. Go to `/admin/media`
2. Click "Upload Files"
3. Drag and drop images
4. Verify they appear in the library

### ✅ Analytics Dashboard
1. Go to `/admin/analytics`
2. Verify statistics are showing
3. Check different time ranges

## 🔧 Troubleshooting

### Issue: "Failed to create project/service"
**Solution**: Run the `fix-rls-policies.sql` script

### Issue: "Storage bucket not found"
**Solution**: 
1. Create bucket manually in Supabase Dashboard
2. Name it exactly: `rcs-media`
3. Make it public

### Issue: "Permission denied"
**Solution**: Check RLS policies are set to allow all operations

### Issue: "400 Bad Request"
**Solution**: 
1. Check database connection
2. Verify table schemas match
3. Run the SQL setup script again

## 📋 What Should Work Now

✅ **Complete CRUD Operations**
- Create, Read, Update, Delete for all content types

✅ **File Upload System**
- Image uploads for projects, blog posts
- Media library management

✅ **Admin Authentication**
- Login/logout functionality
- Protected admin routes

✅ **Content Management**
- Projects with categories and images
- Services with features and benefits
- Blog posts with featured images
- Company information management

✅ **Analytics Dashboard**
- Content statistics
- Recent activity tracking
- Category breakdowns

## 🎯 Next Steps

1. **Customize the design** to match your brand
2. **Add more content types** if needed
3. **Set up proper authentication** with real user accounts
4. **Configure email notifications** for form submissions
5. **Add SEO optimization** features
6. **Set up automated backups**

## 🆘 Still Having Issues?

If you're still getting errors:

1. **Check browser console** for detailed error messages
2. **Verify Supabase connection** in Network tab
3. **Run the SQL script again** - it's safe to run multiple times
4. **Check Supabase logs** in the Dashboard
5. **Make sure all environment variables are correct**

The system should now be fully functional! 🚀
